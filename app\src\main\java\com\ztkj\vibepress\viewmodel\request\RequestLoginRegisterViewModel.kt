package com.ztkj.vibepress.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.request
import com.ztkj.baselib.ext.requestNoCheck
import com.ztkj.baselib.state.ResultState
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.data.model.bean.LoginClient
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity


/**
 * @CreateTime : 2022/10/18 16:45
 * <AUTHOR> AppOS
 * @Description :
 */
class RequestLoginRegisterViewModel : BaseViewModel() {

    var loginResult = MutableLiveData<ResultState<LoginClient>>()

    /**
     * 设备属性查询结果
     */
    var deviceAttributeResult = MutableLiveData<ResultState<TampDeviceEntity>>()

    /**
     * 登录
     */
    fun loginReq(userName: String, password: String) {
        requestNoCheck(
            { apiService.login(userName, password) },
            loginResult,
            true,
            "正在登录中..."
        )
    }

    fun getDeviceAttribute(serialNum: String) {
        request(
            {
                apiService.getDeviceAttribute(serialNum)
            }, deviceAttributeResult,
            true,
            "正在获取设备属性"
        )
    }


}