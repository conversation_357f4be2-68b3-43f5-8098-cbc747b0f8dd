package com.ztkj.vibepress.ui.fragment.setting

import android.os.Bundle
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.util.setOnclickNoRepeat
import com.ztkj.sensorlib.utils.SerialPortUtils
import com.ztkj.vibepress.BR
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.showAvailableBaudrate
import com.ztkj.vibepress.app.ext.showSerialPortDevicePathDialog
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.data.model.bean.SerialConfigBean
import com.ztkj.vibepress.databinding.FragmentSerialParamBinding
import java.lang.ref.WeakReference

/**
 * 串口参数设置界面
 */

class SerialParamFragment : BaseFragment<BaseViewModel, FragmentSerialParamBinding>() {

    private lateinit var serialConfigBean: SerialConfigBean

    override fun initView(savedInstanceState: Bundle?) {
        initDataBinding()
        initToolbar()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        serialConfigBean = CacheUtil.getSerialConfig()
        binding.config = serialConfigBean
    }

    private fun initDataBinding() {
        binding.click = ProxyClick()
    }

    private fun initToolbar() {
        binding.toolbar.run {
            initNormalClose(this@SerialParamFragment)
            setOnclickNoRepeat(this) {
                saveLocalData()
            }
        }
    }


    /**
     * 保存
     */
    private fun saveLocalData() {
        CacheUtil.setSerialConfig(serialConfigBean)
        getString(R.string.save_success).toast()
    }


    inner class ProxyClick {
        fun rtkPath() {
            showSerialPortDevicePathDialog(
                WeakReference(this@SerialParamFragment),
                SerialPortUtils.getAllDevicesPath().toList().indexOf(serialConfigBean.rtkPath)
            ) { _, _, textValue ->
                serialConfigBean.rtkPath = textValue
                notifyPropertyChanged()
            }
        }

        /**
         * RTK波特率
         */
        fun rtkBaudrate() {
            showAvailableBaudrate(
                WeakReference(this@SerialParamFragment),
                SerialPortUtils.getAllBaudrate().toList().indexOf(serialConfigBean.rtkBaudrate)
            ) { _, _, textValue ->
                serialConfigBean.rtkBaudrate = textValue
                notifyPropertyChanged()
            }
        }

        /**
         * 传感器路径选择
         */
        fun sensorPath() {
            showSerialPortDevicePathDialog(
                WeakReference(this@SerialParamFragment),
                SerialPortUtils.getAllDevicesPath().toList().indexOf(serialConfigBean.counterPath)
            ) { _, _, textValue ->
                serialConfigBean.counterPath = textValue
                notifyPropertyChanged()
            }

        }

        /**
         * 传感器波特率
         */
        fun sensorBaudrate() {
            showAvailableBaudrate(
                WeakReference(this@SerialParamFragment),
                SerialPortUtils.getAllBaudrate().toList()
                    .indexOf(serialConfigBean.counterBaudrate)
            ) { _, _, textValue ->
                serialConfigBean.counterBaudrate = textValue
                notifyPropertyChanged()
            }
        }

        private fun notifyPropertyChanged() {
            //TODO 这里的notifyPropertyChange没有起作用
            binding.notifyPropertyChanged(BR.config)
            //改为直接修改
            binding.config = serialConfigBean
        }
    }


}