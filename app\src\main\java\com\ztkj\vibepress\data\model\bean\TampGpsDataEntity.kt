package com.ztkj.vibepress.data.model.bean

import android.annotation.SuppressLint
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * 夯机GPS数据 实体类
 * @CreateTime : 2023/5/18 16:32
 * <AUTHOR> AppOS
 * @Description : 夯机GPS数据 实体类，匹配API返回的数据结构
 */
@SuppressLint("ParcelCreator")
@Parcelize
data class TampGpsDataEntity(
    /**
     * 主键ID
     */
    val id: String = "",

    /**
     * 创建用户
     */
    val createUser: String = "",

    /**
     * 创建部门
     */
    val createDept: String = "",

    /**
     * 创建时间
     */
    val createTime: String = "",

    /**
     * 更新用户
     */
    val updateUser: String = "",

    /**
     * 更新时间
     */
    val updateTime: String = "",

    /**
     * 状态
     */
    val status: Int = 0,

    /**
     * 是否删除
     */
    val isDeleted: Int = 0,

    /**
     * 租户ID
     */
    val tenantId: String = "",

    /**
     * 部门ID
     */
    val deptId: String = "",

    /**
     * 消息类型
     */
    val messageType: String = "GH",

    /**
     * 序列号
     */
    val serialNum: String = "",

    /**
     * 设备ID
     */
    val deviceId: String = "",

    /**
     * 设备名称
     */
    val deviceName: String = "",

    /**
     * 锤半径
     */
    val hammerRadius: String = "-1",

    /**
     * 锤重量
     */
    val hammerWeight: String = "-1",

    /**
     * 作业标准ID
     */
    val jobStandardId: Long = -1,

    /**
     * 发送时间戳
     */
    val sendTimestamp: Long = 0,

    /**
     * 发送时间
     */
    val sendTime: String = "",

    /**
     * 接收时间戳
     */
    val receiveTimestamp: Long = 0,

    /**
     * 车辆纬度
     */
    val carLatitude: String = "",

    /**
     * 车辆经度
     */
    val carLongitude: String = "",

    /**
     * 海拔高度
     */
    val altitude: String = "-1",

    /**
     * GPS状态
     */
    val gpsStatus: Int = 0,

    /**
     * 卫星数量
     */
    val satelliteNum: Int = -1,

    /**
     * 差分延迟
     */
    val differentialDelay: String = "",

    /**
     * 差分站
     */
    val differentialStation: String = "",

    /**
     * 椭球高度
     */
    val ellipsoidalHeight: String = "",

    /**
     * 方向角
     */
    val orientation: String = "",

    /**
     * 速度
     */
    val speed: String = "",

    /**
     * 锤纬度
     */
    val hammerLatitude: String = "-1",

    /**
     * 锤经度
     */
    val hammerLongitude: String = "-1",

    /**
     * 传感器值
     */
    val sensorValue: Int = -1,

    /**
     * 锤高度
     */
    val hammerHeight: String = "-1",

    /**
     * 锤状态
     */
    val hammerState: Int = -1,

    /**
     * 张力状态
     */
    val tensionState: Int = -1,

    /**
     * 张力值
     */
    val tensionValue: String = "-1",

    /**
     * 张力变化
     */
    val tensionChange: Int = -1,

    /**
     * 距离
     */
    val distance: String = "-1",

    /**
     * 液压状态
     */
    val hydraulicStatus: Int = -1
) : Parcelable, Serializable