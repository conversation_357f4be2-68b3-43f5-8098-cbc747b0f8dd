package com.ztkj.vibepress.data.model.bean

/**
 * @CreateTime : 2023/5/18 16:32
 * <AUTHOR> AppOS
 * @Description :
 */

data class TampGpsDataEntity(
    var messageType: String = "H01",
    var serialNum: String? = "",
    var sendTimestamp: String = "",
    var carLatitude: String = "",
    var carLongitude: String = "",
    var altitude: String = "",
    var gpsStatus: String = "",
    var satelliteNum: String = "",
    var differentialDelay: String = "",
    var differentialStation: String = "",
    var ellipsoidalHeight: String = "",
    var orientation: String = "",
    var speed: String = "",
    var hammerLatitude: String = "",
    var hammerLongitude: String = "",
    var sensorValue: Int = 0,
    var hammerHeight: Double = 0.0,
    var hammerState: Int = 0,
    var tensionState: Int = 0,
    var tensionChange: Int = 0,
    /**
     * 锤半径
     */
    var hammerRadius: Double = 0.0,
    /**
     * 锤重
     */
    var hammerWeight: Double = 0.0,
    /**
     *作业标准ID
     */
    var jobStandardId: String? = null,
    /**
     * 张力原始值
     */
    var tensionValue: Float = 0.0f,
    /**
     * 校准系数
     */
    var calibration: Float = 1.0f
)