package com.ztkj.vibepress.app.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import kotlinx.coroutines.flow.Flow

/**
 * @CreateTime : 2023/6/5 19:49
 * <AUTHOR> AppOS
 * @Description :
 */
@Dao
interface TampDeviceDao {
    @Query("DELETE FROM TampDeviceEntity")
    suspend fun deleteAll()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTampDevice(tampDevice: TampDeviceEntity)

    @Query("SELECT * FROM TampDeviceEntity LIMIT 1")
    suspend fun getTampDevice(): TampDeviceEntity?

    @Query("SELECT * FROM TampDeviceEntity LIMIT 1")
    fun getTampDeviceFlow(): Flow<TampDeviceEntity?>
}