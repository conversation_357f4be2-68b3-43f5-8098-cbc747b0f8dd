package com.ztkj.vibepress.app.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.data.model.bean.VibeMetaData

/**
 * @CreateTime : 2023/5/16 10:15
 * <AUTHOR> AppOS
 * @Description :
 */
@Database(
    entities = [VibeMetaData::class, TampDeviceEntity::class, JobStandardEntity::class],
    version = 3,
    exportSchema = false
)
@TypeConverters(Converter::class)
abstract class AppDatabase : RoomDatabase() {

    abstract fun vibeDao(): VibeDao

    abstract fun tampDeviceDao(): TampDeviceDao

    abstract fun jobStandardDao(): JobStandardDao
}