package com.ztkj.imext.im.handler;

import android.util.Log;

import com.ztkj.im.IMSConfig;
import com.ztkj.imext.bean.AppMessage;
import com.ztkj.imext.bean.SingleMessage;
import com.ztkj.imext.event.CEventCenter;
import com.ztkj.imext.event.Events;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       ServerReportMessageHandler.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im.handler</p>
 * <b>
 * <p>@Description:     服务端返回的消息发送状态报告</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/22 19:16</p>
 * <p>@email:           <EMAIL></p>
 */
public class ServerReportMessageHandler extends AbstractMessageHandler {

    private static final String TAG = ServerReportMessageHandler.class.getSimpleName();

//    DBManager dbManager = DBManager.getInstance(ZTApplication.sharedInstance());

    @Override
    protected void action(AppMessage message) {
        //超过超时次数后，消息会转发到应用层，这里收到了消息发送失败的日志
        //2023-05-19 发送的数据全部转发到应用层,方便监测数据状态
        Log.d(TAG, "收到消息状态报告，message=" + message);
        SingleMessage msg = new SingleMessage();
        msg.setMsgId(message.getHead().getMsgId());
        msg.setMsgType(message.getHead().getMsgType());
        msg.setMsgContentType(message.getHead().getMsgContentType());
        msg.setFromId(message.getHead().getFromId());
        msg.setToId(message.getHead().getToId());
        msg.setTimestamp(message.getHead().getTimestamp());
        msg.setExtend(message.getHead().getExtend());
        msg.setStatusReport(message.getHead().getStatusReport());
        msg.setContent(message.getBody());
        CEventCenter.dispatchEvent(Events.CHAT_REPORT_MESSAGE, message.getHead().getStatusReport(), 0, msg);
/*        if(message.getHead().getStatusReport()== IMSConfig.DEFAULT_REPORT_SERVER_SEND_MSG_FAILURE){
            //TODO
//            GpsMessage gpsMessage = new GpsMessage();
//            gpsMessage.setMsgId(message.getHead().getMsgId());
//            gpsMessage.setDeviceId(message.getHead().getFromId());
//            gpsMessage.setTimestamp(message.getHead().getTimestamp());
//            gpsMessage.setContent(message.getBody());
//            dbManager.insertGpsMessage(gpsMessage);
        }else {
//TODO            NmeaAnalysisUtil.getInstance().setYsResponseData(message.getBody());
        }*/
    }
}
