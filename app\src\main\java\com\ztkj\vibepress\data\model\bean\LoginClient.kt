package com.ztkj.vibepress.data.model.bean

import android.annotation.SuppressLint
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ztkj.vibepress.data.model.kEnum.TampRole
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * @CreateTime : 2022/10/17 10:00
 * <AUTHOR> AppOS
 * @Description :
 */
@SuppressLint("ParcelCreator")
@Parcelize
data class LoginClient(
    @SerializedName("access_token") val accessToken: String,
    val account: String,
    val avatar: String,
    val client_id: String,
    val dept_id: String,
    val detail: Detail,
    val expires_in: Int,
    val jti: String,
    val license: String,
    val nick_name: String,
    val oauth_id: String,
    val post_id: String,
    val real_name: String,
    @SerializedName("refresh_token") val refreshToken: String,
    val role_id: String,
    val role_name: String,
    val scope: String,
    val tenant_id: String,
    val token_type: String,
    val user_id: String,
    val user_name: String
) : Parcelable, Serializable {
    @IgnoredOnParcel
    val tampRole: TampRole =
        if (role_name.contains("tampDriver")) TampRole.MACHINIST else TampRole.OPERATOR
}

@Parcelize
data class Detail(
    val type: String
) : Parcelable, Serializable