package com.ztkj.vibepress.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.QuickViewHolder
import com.ztkj.vibepress.R
import com.ztkj.vibepress.databinding.ItemSimpleRecyclerviewTextBinding
import javax.inject.Inject

/**
 * @CreateTime : 2023/7/10 17:38
 * <AUTHOR> AppOS
 * @Description :
 */
class SimpleAdapter @Inject constructor() : BaseQuickAdapter<String, QuickViewHolder>() {
    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int, item: String?) {
        holder.getView<TextView>(R.id.content).text = item
    }


    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): QuickViewHolder {
        return QuickViewHolder(R.layout.item_simple_recyclerview_text,parent)
    }
}