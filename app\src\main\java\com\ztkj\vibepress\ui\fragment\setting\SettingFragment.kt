package com.ztkj.vibepress.ui.fragment.setting

import android.os.Bundle
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.navigateAction
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.showNormalMaterialDialog
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.data.model.bean.SettingItem
import com.ztkj.vibepress.data.model.kEnum.TampRole
import com.ztkj.vibepress.databinding.FragmentSettingBinding
import com.ztkj.vibepress.ui.adapter.SettingAdapter
import com.ztkj.vibepress.ui.adapter.SettingItemSpacingDecoration
import dagger.hilt.android.AndroidEntryPoint
import java.lang.ref.WeakReference
import javax.inject.Inject
import javax.inject.Named

/**
 * App System Settings
 */
@AndroidEntryPoint
class SettingFragment : BaseFragment<BaseViewModel, FragmentSettingBinding>() {

    @Inject
    lateinit var settingAdapter: SettingAdapter

    @Named("SettingItem")
    @Inject
    lateinit var settingItemSpacingDecoration: SettingItemSpacingDecoration

    override fun initView(savedInstanceState: Bundle?) {
        initToolbar()
        initRecyclerView()
    }

    private fun initToolbar() {
        binding.toolbar.initNormalClose(this)
    }

    private fun initRecyclerView() {
        binding.rvData.adapter = settingAdapter
        binding.rvData.addItemDecoration(settingItemSpacingDecoration)

        settingAdapter.setOnItemClickListener { _, _, position ->
            //Navigate to Different Pages Based on Different navigation actionID
            val data = settingAdapter.items[position]
            /*            if (data.actionId == R.id.action_settingFragment_to_calibrationFragment) {
                            showNormalMaterialDialog(
                                WeakReference(this),
                                content = "进入校准后，将自动清除计数器数据，是否确定?"
                            ) {
                                nav().navigateAction(R.id.action_settingFragment_to_calibrationFragment)
                            }
                        } else {
                            nav().navigateAction(data.actionId)
                        }*/
            nav().navigateAction(data.actionId)
        }

    }

    override fun lazyLoadData() {
        super.lazyLoadData()
//        val roleType = CacheUtil.getUser()?.tampRole
        settingAdapter.submitList(opeartorData)
    }

    private val opeartorData: ArrayList<SettingItem>
        get() = arrayListOf(
            SettingItem(
                getString(R.string.diagnosis),
                R.mipmap.item_setting_diagnosis,
                R.id.action_settingFragment_to_diagnosisFragment
            ),
            SettingItem(
                getString(R.string.operating_standards),
                R.mipmap.operating_standards,
                R.id.action_settingFragment_to_jobStandardListFragment
            ),
            SettingItem(
                getString(R.string.calibration),
                R.mipmap.calibration,
                R.id.action_settingFragment_to_calibrationFragment
            ),
            SettingItem(
                getString(R.string.about),
                R.mipmap.about,
                R.id.action_settingFragment_to_aboutFragment
            )
        )

}