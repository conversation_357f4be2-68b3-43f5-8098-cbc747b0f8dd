package com.ztkj.baselib.location;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.SystemClock;

import com.ztkj.baselib.base.Ktx;

/**
 * @CreateTime : 2022/10/20 15:27
 * <AUTHOR> AppOS
 * @Description :  使用AlarmManager实现定时任务，唤醒CPU
 * (目前手机设备在长时间黑屏或锁屏时CPU会休眠，这导致定位SDK不能正常进行位置更新)
 */
public class AlarmManagerUtils {
    /**
     * 管理器
     */
    private static AlarmManager alarmManager;
    /**
     * 延迟Intent
     */
    private static PendingIntent pendingIntent;

    /**
     * 唤醒CPU的广播action
     */
    private static final String wakeupCpuAction = "com.ztkj.wakeup_cpu";


    /**
     * 开始执行
     */
    public static void start() {
        //注册广播
        Context applicationContext = Ktx.app.getApplicationContext();
        IntentFilter filter = new IntentFilter();
        filter.addAction(wakeupCpuAction);
        applicationContext.registerReceiver(alarmReceiver,filter);
        //AlarmManager实质是一个全局的定时器，是Android中常用的一种系统级别的提示服务，
        // 在指定时间或周期性启动其它组件（包括Activity,Service,BroadcastReceiver）
        alarmManager = (AlarmManager)  applicationContext.getSystemService(Context.ALARM_SERVICE);

        //构建pendingIntent
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        //设置发送广播的action
        intent.setAction(wakeupCpuAction);
        pendingIntent = PendingIntent.getBroadcast( applicationContext, 0, intent, 0);

        /**
         * AlarmManager.ELAPSED_REALTIME:不会唤醒系统,所用的时间是相对时间,SystemClock.elapsedRealtime()
         * AlarmManager.ELAPSED_REALTIME_WAKEUP:能唤醒系统.
         * AlarmManager.RTC:不会唤醒系统,所用时间是绝地时间,System.currentTimeMillis()
         * AlarmManager.RTC_WAKEUP:能唤醒系统
         * AlarmManager.POWER_OFF_WAKEUP:表示闹钟在手机关机状态下也能正常进行提示功能
         */
        //设置定时任务
        alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP,
                SystemClock.elapsedRealtime(), pendingIntent);
    }

    /**
     * 停止执行
     */
    public static void stop() {
        if (alarmManager != null && pendingIntent != null) {
            alarmManager.cancel(pendingIntent);
            //注销广播
            Ktx.app.getApplicationContext().unregisterReceiver(alarmReceiver);
            alarmManager = null;
            pendingIntent = null;
        }
    }


    /**
     * 广播接收器
     */
    private static final BroadcastReceiver alarmReceiver = new BroadcastReceiver() {
        //循环时长
        private final long TIME_INTERVAL = 10 * 1000L;

        @Override
        public void onReceive(Context context, Intent intent) {
            // 重复定时任务
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    SystemClock.elapsedRealtime() + TIME_INTERVAL, pendingIntent);
        }
    };

}
