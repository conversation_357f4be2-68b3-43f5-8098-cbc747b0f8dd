<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:background="@drawable/shape_header_button"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_save"
        android:layout_gravity="center_vertical" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        android:layout_marginStart="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="@string/save"
        android:layout_gravity="center_vertical" />

</LinearLayout>