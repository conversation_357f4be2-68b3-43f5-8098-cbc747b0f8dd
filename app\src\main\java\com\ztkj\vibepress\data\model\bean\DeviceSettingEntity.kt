package com.ztkj.vibepress.data.model.bean

import android.text.InputType

/**
 * @CreateTime : 2023/6/2 9:34
 * <AUTHOR> AppOS
 * @Description :
 */
data class DeviceSettingEntity(
    /**
     * 标题
     */
    val title: String,
    /**
     * 默认显示名称
     */
    var defaultValue: String? = null,
    /**
     * 默认的类型设置为文本
     */
    val fieldType: FieldType = FieldType.TEXT_FIELD,
    /**
     * 输入法类型
     */
    val inputType: Int = InputType.TYPE_NUMBER_FLAG_DECIMAL,
    var spinnerList: Int? = null
)

enum class FieldType {
    TEXT_FIELD,
    DROPDOWN_LIST
}
