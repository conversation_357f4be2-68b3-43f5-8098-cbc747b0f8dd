package com.ztkj.baselib.location;

import com.amap.api.location.AMapLocation;

import java.util.Objects;

/**
 * @CreateTime : 2022/10/20 15:29
 * <AUTHOR> AppOS
 * @Description : 定位信息model
 */
public class LocationModel {
    public double latitude; //获取纬度
    public double longitude;//获取经度
    public float accuracy;//获取精度信息
    public String address;//地址
    public String country;//国家信息
    public String province;//省信息
    public String city;//城市信息
    public String district;//城区信息
    public String street;//街道信息
    public String streetNum;//街道门牌号信息
    public String cityCode;//城市编码
    public String adCode;//地区编码
    public int locationType;//获取当前定位结果来源
    public String aoiName;//获取当前定位点的AOI信息
    public String buildingId;//获取当前室内定位的建筑物Id
    public String floor;//获取当前室内定位的楼层
    public int gpsAccuracyStatus;//获取GPS的当前状态
    public long time;//获取定位时间

    public void build(AMapLocation aMapLocation) {
        this.latitude = aMapLocation.getLatitude();
        this.longitude = aMapLocation.getLongitude();
        this.accuracy = aMapLocation.getAccuracy();
        this.address = aMapLocation.getAddress();
        this.country = aMapLocation.getCountry();
        this.province = aMapLocation.getProvince();
        this.city = aMapLocation.getCity();
        this.district = aMapLocation.getDistrict();
        this.street = aMapLocation.getStreet();
        this.streetNum = aMapLocation.getStreetNum();
        this.cityCode = aMapLocation.getCityCode();
        this.adCode = aMapLocation.getAdCode();
        this.locationType = aMapLocation.getLocationType();
        this.aoiName = aMapLocation.getAoiName();
        this.buildingId = aMapLocation.getBuildingId();
        this.floor = aMapLocation.getFloor();
        this.gpsAccuracyStatus = aMapLocation.getGpsAccuracyStatus();
        this.time = aMapLocation.getTime();
    }

    @Override
    public String toString() {
        return "LocationModel{" +
                "latitude=" + latitude +
                ", longitude=" + longitude +
                ", accuracy=" + accuracy +
                ", address='" + address + '\'' +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", street='" + street + '\'' +
                ", streetNum='" + streetNum + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", adCode='" + adCode + '\'' +
                ", locationType=" + locationType +
                ", aoiName='" + aoiName + '\'' +
                ", buildingId='" + buildingId + '\'' +
                ", floor='" + floor + '\'' +
                ", gpsAccuracyStatus=" + gpsAccuracyStatus +
                ", time=" + time +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LocationModel that = (LocationModel) o;
        return Double.compare(that.latitude, latitude) == 0 && Double.compare(that.longitude, longitude) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(latitude, longitude);
    }
}
