package com.ztkj.vibepress.di

import android.content.Context
import androidx.room.Room
import com.ztkj.vibepress.app.db.AppDatabase
import com.ztkj.vibepress.app.db.JobStandardDao
import com.ztkj.vibepress.app.db.TampDeviceDao
import com.ztkj.vibepress.app.db.VibeDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * @CreateTime : 2023/5/16 10:16
 * <AUTHOR> AppOS
 * @Description :
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideVibeDao(appDatabase: AppDatabase): VibeDao {
        return appDatabase.vibeDao()
    }

    @Provides
    @Singleton
    fun provideTampDevice(appDatabase: AppDatabase): TampDeviceDao = appDatabase.tampDeviceDao()

    @Provides
    @Singleton
    fun provideJobStandardDao(appDatabase: AppDatabase): JobStandardDao =
        appDatabase.jobStandardDao()

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext appContext: Context): AppDatabase {
        return Room.databaseBuilder(
            appContext,
            AppDatabase::class.java,
            "vibepress.db"
        )
            //破坏性迁移
            .fallbackToDestructiveMigration()
            .build()
    }
}