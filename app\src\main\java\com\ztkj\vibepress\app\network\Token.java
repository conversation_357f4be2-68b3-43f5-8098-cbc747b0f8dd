package com.ztkj.vibepress.app.network;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @CreateTime : 2022/12/1 10:48
 * <AUTHOR> AppOS
 * @Description : 自定义注解，判断需不需要
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Token {
    boolean value() default true;
}
