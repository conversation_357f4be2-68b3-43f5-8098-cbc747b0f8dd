package com.ztkj.netty;

import com.ztkj.im.listener.OnEventListener;
import com.ztkj.im.protobuf.MessageProtobuf;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMSClientInterface.java</p>
 * <p>@PackageName:     com.freddy.im.interf</p>
 * <b>
 * <p>@Description:     ims抽象接口，需要切换到其它方式实现im功能，实现此接口即可</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/03/31 20:04</p>
 * <p>@email:           <EMAIL></p>
 */
public interface TcpClientInterface {

    void init(String host, OnEventListener listener);

    /**
     * 重置连接，也就是重连
     * 首次连接也可认为是重连
     */
    void resetConnect();

    /**
     * 重置连接，也就是重连
     * 首次连接也可认为是重连
     * 重载
     *
     * @param isFirst 是否首次连接
     */
    void resetConnect(boolean isFirst);

    /**
     * 关闭连接，同时释放资源
     */
    void close();

    /**
     * 标识ims是否已关闭
     *
     * @return
     */
    boolean isClosed();

    /**
     * 发送消息
     *
     * @param msg
     */
    void sendMsg(String msg);

    /**
     * 获取重连间隔时长
     *
     * @return
     */
    int getReconnectInterval();

    /**
     * 获取连接超时时长
     *
     * @return
     */
    int getConnectTimeout();

    /**
     * 接收消息，并通过OnEventListener转发消息到应用层
     * @param msg
     */
    void receivedMsg(MessageProtobuf.Msg msg);


}
