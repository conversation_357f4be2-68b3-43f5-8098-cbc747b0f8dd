package com.ztkj.baselib.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * @CreateTime : 2022/10/19 15:42
 * <AUTHOR> AppOS
 * @Description :
 */
public class MD5Util {
    public static String encodeToMD5(String string) {
        byte[] hash = new byte[0];
        try {
            hash = MessageDigest.getInstance("MD5").digest(
                    string.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
        }
        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) {
                hex.append("0");
            }
            hex.append(Integer.toHexString(b & 0xFF));
        }
        return hex.toString();
    }
}
