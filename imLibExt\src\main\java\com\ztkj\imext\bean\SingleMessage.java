package com.ztkj.imext.bean;

import com.blankj.utilcode.util.StringUtils;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       SingleMessage.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.bean</p>
 * <b>
 * <p>@Description:     单聊消息</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/10 03:24</p>
 * <p>@email:           <EMAIL></p>
 */
public class SingleMessage extends ContentMessage implements Cloneable {

    @Override
    public int hashCode() {
        try {
            return this.msgId.hashCode();
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return 1;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }

        if (!(obj instanceof SingleMessage)) {
            return false;
        }

        return StringUtils.equals(this.msgId, ((SingleMessage) obj).getMsgId());
    }
}
