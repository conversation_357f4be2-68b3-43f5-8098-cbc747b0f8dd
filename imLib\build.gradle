plugins {
    id 'com.android.library'
}

android {
    namespace 'com.ztkj.im'
    compileSdk 32

    defaultConfig {
        minSdk 23
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    packagingOptions {
        resources {
            excludes += ['META-INF/LGPL2.1', 'META-INF/LICENSE', 'META-INF/NOTICE', 'META-INF/io.netty.versions.properties']
        }
    }

}

dependencies {
    implementation project(path: ':baseLib')
    api 'com.github.netty.netty:netty-all:netty-4.1.92.Final'
    api 'com.google.protobuf:protobuf-java:3.22.3'
}