package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.callback.databinding.BooleanObservableField
import com.ztkj.baselib.callback.databinding.StringObservableField
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType

class MultiSettingViewModel : BaseViewModel() {


    /**
     * 传感器端口
     */
    val sensorPort = StringObservableField()

    /**
     * 传感器波特率
     */
    val sensorBaudRate = StringObservableField()

    val sensorDataValue = StringObservableField()

    val serviceStatus = MutableLiveData(false)

    /**
     * 更新频率
     */
    val updateFrequency = StringObservableField()

    /**
     * GNSS传感器数据
     */
    var gnssValue = MutableLiveData<String>()

    /**
     * 计数器传感器数据
     */
    var counterValue = MutableLiveData<String>()

    var boardType = MutableLiveData(ServiceDataType.GNSS_ALL)

    /**
     * 断电清零
     */
    var powerOffClear = BooleanObservableField(false)

    /**
     * 计数器换向
     */
    val counterReversing = BooleanObservableField(false)
    
    /**
     * 张力阈值
     */
    val tensionThreshold = StringObservableField()


}