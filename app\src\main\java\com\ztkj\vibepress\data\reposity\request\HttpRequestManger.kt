package com.ztkj.vibepress.data.reposity.request

import com.ztkj.baselib.network.AppException
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.data.model.bean.ApiResponse
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

/**
 * @CreateTime : 2023/7/28 14:09
 * <AUTHOR> AppOS
 * @Description :
 */


object HttpRequestManger {

    suspend fun login(userName: String, passWord: String): ApiResponse<TampDeviceEntity> {
        val loginClient = apiService.login(userName, passWord)
        if (loginClient.accessToken.isNotEmpty()) {
            return apiService.getDeviceAttribute(userName)
        } else {
            throw AppException(401, "登录失败")
        }

    }
}