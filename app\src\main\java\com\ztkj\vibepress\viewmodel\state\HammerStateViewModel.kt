package com.ztkj.vibepress.viewmodel.state

import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.callback.databinding.StringObservableField

/**
 * @CreateTime : 2023/8/3 10:19
 * <AUTHOR> AppOS
 * @Description :
 */
class HammerStateViewModel : BaseViewModel() {

    /**
     * 夯锤半径
     */
    var hammerRadius = StringObservableField()

    /**
     * 夯锤重量
     */
    var hammerWeight = StringObservableField()

    /**
     * 当前作业类型： 点夯、满夯
     */
    var workTypeString = StringObservableField()

    /**
     * 要求夯击能
     */
    var requireEnergy = StringObservableField()

    /**
     * 要求夯机次数
     */
    var requireHitCount = StringObservableField("0")

    /**
     * 实际夯机次数
     */
    var realHitCount = StringObservableField("0")

    /**
     * 当前桩号
     */
    var currentStake = StringObservableField()

    /**
     * 锤高
     */
    var hammerHeight = StringObservableField()


}