package com.ztkj.vibepress.data.model.bean

import android.annotation.SuppressLint
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.data.model.kEnum.TampWorkType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType.POINT
import kotlinx.parcelize.Parcelize
import java.io.Serializable
import java.math.BigDecimal

/**
 *
 * 夯机作业标准 实体类
 * @CreateTime : 2023/5/30 15:02
 * <AUTHOR> AppOS
 * @Description :夯机作业标准 实体类
 */
@Entity
@SuppressLint("ParcelCreator")
@Parcelize
data class JobStandardEntity(
    @PrimaryKey(autoGenerate = true) val uID: Long = 0L,
    /**
     * 名称
     */
    @ColumnInfo(name = "name") val name: String,
    /**
     * 序列号
     */
    @ColumnInfo(name = "serialNum") var serialNum: String = SerialNumberUtil.getSerial(),
    /**
     * 作业模式, 点夯 0,满夯 1
     */
    @ColumnInfo(name = "workType") val workType: Int,
    /**
     * 要求夯击能(KN·m)
     */
    @ColumnInfo(name = "requireEnergy") val requireEnergy: BigDecimal,
    /**
     *  要求间距
     */
    @ColumnInfo(name = "requireDistance") val requireDistance: BigDecimal,
    /**
     * 要求夯击次数
     */
    @ColumnInfo(name = "requireTimes") val requireTimes: Int,
    /**
     * 锤半径
     */
    @ColumnInfo(name = "hammerRadius") var hammerRadius: Double,
    /**
     * 锤重
     */
    @ColumnInfo(name = "hammerWeight") var hammerWeight: Double,
    /**
     *过滤阈值
     */
    /*    @ColumnInfo(name = "threshold") val threshold: BigDecimal,
        */
    /**
     * 点夯聚合半径(cm)
     *//*
    @ColumnInfo(name = "clusterRadius") val clusterRadius: BigDecimal,*/
    /**
     * 做修改的时候用的 服务端传入参数ID
     */
    var id: String?

) : Parcelable, Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

    /**
     * 工作类型
     */
    val tampWorkType: TampWorkType
        get() = if (workType == 0) POINT else TampWorkType.FULL

    val descTampWorkType: String
        get() = if (tampWorkType == POINT) "点夯" else "满夯"

    fun toVisibleString(): String {
        return "名称:\t $name\t\t类型:\t${descTampWorkType}\t\t提升次数:\t${requireTimes}\t\t夯锤锤重:\t${hammerWeight}t"
    }

    fun checkValidData(): Boolean {
        // 校验requireTimes
        requireTimes.let {
            if (it < 1 || it > 100) {
//                throw IllegalArgumentException("requireTimes 必须在 1 到 100 之间")
                return false
            }
        }

        // 校验requireEnergy
        requireEnergy.let {
            if (it < BigDecimal.ONE || it > BigDecimal.valueOf(20000)) {
                return false
//                throw IllegalArgumentException("requireEnergy 必须在 1 到 20000 之间")
            }
        }

        // 校验hammerRadius
        hammerRadius.let {
            if (it < 0.1 || it > 2.5) {
                return false
//                throw IllegalArgumentException("hammerRadius 必须在 0.1 到 2.5 之间")
            }
        }

        // 校验hammerWeight
        hammerWeight.let {
            if (it < 0 || it > 100) {
                return false
//                throw IllegalArgumentException("hammerWeight 必须在 0 到 100 之间")
            }
        }
        return true
    }
}

