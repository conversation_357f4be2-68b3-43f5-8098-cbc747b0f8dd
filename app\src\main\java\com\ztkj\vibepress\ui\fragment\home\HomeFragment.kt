package com.ztkj.vibepress.ui.fragment.home

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.geometry.SpatialReferences
import com.google.gson.Gson
import com.xuexiang.xupdate.easy.EasyUpdate
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.navigateAction
import com.ztkj.sensorlib.model.GpsdataInfo
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.showCreateJobStandardDialog
import com.ztkj.vibepress.app.util.CalculateUtil
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants.UPDATE.Companion.APP_UPDATE_URL
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.data.model.kEnum.ServiceStatusType
import com.ztkj.vibepress.databinding.FragmentHomeBinding
import com.ztkj.vibepress.eventViewModel
import com.ztkj.vibepress.service.BackgroundService
import java.lang.ref.WeakReference


class HomeFragment : BaseFragment<BaseViewModel, FragmentHomeBinding>() {

    private var boundService: BackgroundService? = null


    override fun initView(savedInstanceState: Bundle?) {
        binding.toolbar.run {
            setSerialNumber(getString(R.string.sn_format, SerialNumberUtil.getSerial()))
            settingClick {
                nav().navigateAction(R.id.action_global_settingFragment)
            }

        }
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        checkUpdate()
    }

    override fun onResume() {
        super.onResume()
        val serviceStatusType = eventViewModel.serviceStatusType.value
        serviceStatusType?.let {
            if (it < ServiceStatusType.STARTING) {
                bindService()
            }

        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unBindService()
    }

    /**
     * 绑定Service
     */
    private fun bindService() {
        try {
            eventViewModel.serviceStatusType.value = ServiceStatusType.STARTING
            val intent = Intent(requireContext(), BackgroundService::class.java)
            requireContext().startService(intent)
            //绑定时自动启动服务
            requireContext().bindService(intent, mServiceConnection, 0)
        } catch (e: Exception) {
            e.printStackTrace()
            eventViewModel.serviceStatusType.value = ServiceStatusType.STOPPED
        }
    }

    override fun createObserver() {
        super.createObserver()
        NmeaAnalysisUtil.getInstance().cfResponseLiveData.observe(this) {
            boundService?.receiveCommand(ServiceDataType.GNSS_ALL, it)
        }

        eventViewModel.currentSensorValue.observeInFragment(this@HomeFragment) {
            val pair = CalculateUtil.parseSensorCounter(it)
            if (pair?.first != null) {
                binding.toolbar.setTension(
                    pair.first.toString(),
                    CalculateUtil.tensionValue.toString()
                )
            }
            binding.toolbar.setGpsInfo(
                NmeaAnalysisUtil.getInstance().statusType.toString(),
                NmeaAnalysisUtil.getInstance().lockGnssCount.toString()
            )
        }
    }

    private fun unBindService() {
        if (boundService != null) {
            boundService = null
            requireContext().unbindService(mServiceConnection)
        }
    }


    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                eventViewModel.serviceStatusType.value = ServiceStatusType.RUNNING
                /*          boundService?.setOnDataChangedCallback { serviceDataType, data ->
                              handleServiceData(serviceDataType, data)
                          }*/
            }

        }

        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
            eventViewModel.serviceStatusType.value = ServiceStatusType.STOPPED
        }

    }

    private fun checkUpdate() {
        EasyUpdate.checkUpdate(requireContext(), APP_UPDATE_URL)
    }
}
