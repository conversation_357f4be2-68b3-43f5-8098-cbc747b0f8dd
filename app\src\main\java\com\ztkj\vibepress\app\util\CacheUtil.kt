package com.ztkj.vibepress.app.util

import com.ztkj.baselib.ext.util.notNull
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants
import com.ztkj.vibepress.data.Constants.CONFIG.Companion.APP_TYPE
import com.ztkj.vibepress.data.Constants.CONFIG.Companion.CURRENT_JOB_STANDARD_ENTITY
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.LoginClient
import com.ztkj.vibepress.data.model.bean.NetConfigBean
import com.ztkj.vibepress.data.model.bean.SerialConfigBean
import com.ztkj.vibepress.data.model.kEnum.AppType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType

/**
 * @CreateTime : 2022/10/18 10:18
 * <AUTHOR> AppOS
 * @Description :
 */
object CacheUtil {

    private const val APP_FIRST_LOGIN = "APP_FIRST_LOGIN"


    fun getUser(): LoginClient? =
        SpUtils.getObject(Constants.USER.USER_INFO, LoginClient::class.java)


    @JvmStatic
    fun setUser(userInfo: LoginClient?) {
        userInfo.notNull({
            SpUtils.putObject(Constants.USER.USER_INFO, userInfo)
            setIsLogin(true)
        }, {
            SpUtils.removeValueForKey(Constants.USER.USER_INFO)
            setIsLogin(false)
        })
    }


    /**
     * 是否是第一次登陆
     */
    fun isFirst(): Boolean {
        return SpUtils.getBoolean(APP_FIRST_LOGIN, false)
    }

    /**
     * 设置是否是第一次登陆
     */
    fun setFirst(isFirst: Boolean) {
        SpUtils.putBoolean(APP_FIRST_LOGIN, isFirst)
    }

    /**
     * 获取 accessToken
     */
    @JvmStatic
    fun getAccessToken(): String? {
        return getUser()?.accessToken
    }

    /**
     * 设置accessToken
     */
    fun setAccessToken(accessToken: String) {
        SpUtils.putString(Constants.LOGIN.ACCESS_TOKEN, accessToken)
    }

    /**
     * 获取refreshToken
     */
    @JvmStatic
    fun getRefreshToken(): String? = getUser()?.refreshToken

    /**
     * 设置refreshToken
     */
    fun setRefreshToken(refreshToken: String) {
        SpUtils.putString(Constants.LOGIN.REFRESH_TOKEN, refreshToken)
    }

    fun isLogin(): Boolean = SpUtils.getBoolean(Constants.LOGIN.IS_LOGIN, false)

    fun setIsLogin(isLogin: Boolean) {
        SpUtils.putBoolean(Constants.LOGIN.IS_LOGIN, isLogin)
    }


    /**
     * 获取配置的张力阈值
     */
    fun getTensionValue(): Float {
        return SpUtils.getFloat(Constants.DEVICE.DEFAULT_TENSION, 0.5f)
    }

    /**
     * 设置张力值
     */
    fun setTensionValue(tensionValue: Float) {
        SpUtils.putFloat(Constants.DEVICE.DEFAULT_TENSION, tensionValue)
    }

    fun setNetworkConfig(netConfigBean: NetConfigBean) {
        netConfigBean.notNull({
            SpUtils.putObject(Constants.CONFIG.NETWORK_CONFIG, netConfigBean)
        }, {
            SpUtils.removeValueForKey(Constants.CONFIG.NETWORK_CONFIG)
        })
    }

    /**
     * 获取串口参数
     */
    fun getSerialConfig(): SerialConfigBean =
        SpUtils.getObject(Constants.CONFIG.SERIAL_PORT_CONFIG, SerialConfigBean::class.java)
            ?: VibeConfig.getDefaultSerialPortConfigBean()

    /**
     * 设置串口参数
     */
    fun setSerialConfig(serialConfigBean: SerialConfigBean) {
        serialConfigBean.notNull({
            SpUtils.putObject(Constants.CONFIG.SERIAL_PORT_CONFIG, serialConfigBean)
        }, {
            SpUtils.removeValueForKey(Constants.CONFIG.SERIAL_PORT_CONFIG)
        })
    }

    fun getNetworkConfig(): NetConfigBean =
        SpUtils.getObject(Constants.CONFIG.NETWORK_CONFIG, NetConfigBean::class.java)
            ?: VibeConfig.getDefaultNetworkConfigBean()

    /**
     * 锤高系数，用于计数传感器和提升高度之间的换算关系
     */
    fun getHammerHeightRatio(): Float = SpUtils.getFloat(Constants.CONFIG.HAMMER_HEIGHT_RATIO, 1f)

    /**
     * 设置锤高系数
     */
    fun setHammerHeightRatio(value: Float) {
        SpUtils.putFloat(Constants.CONFIG.HAMMER_HEIGHT_RATIO, value)
    }

    fun setAppType(appType: AppType) {
        SpUtils.putObject(APP_TYPE, appType)
    }

    /**
     * AppType:Online Offline
     * 默认使用在线版本
     */
    fun getAppType(): AppType = SpUtils.getObject(APP_TYPE, AppType::class.java) ?: AppType.ONLINE

    fun setJobStandard(jobStandardEntity: JobStandardEntity) {
        appViewModel.jobStandardEntity.postValue(jobStandardEntity)
        SpUtils.putObject(CURRENT_JOB_STANDARD_ENTITY, jobStandardEntity)
    }

    /**
     * 获取当前的作业标准
     */
    fun getCurrentJobStandard(): JobStandardEntity? {
        return SpUtils.getObject(CURRENT_JOB_STANDARD_ENTITY, JobStandardEntity::class.java)
    }

    /**
     * 获取当前的工作类型
     */
    fun getCurrentWorkType(): TampWorkType? {
        val currentJobStandard = getCurrentJobStandard()
        return when (currentJobStandard?.workType) {
            0 -> TampWorkType.POINT
            1 -> TampWorkType.FULL
            else -> null
        }

    }


    /**
     * 重置信息
     */
    fun restore() {
        setFirst(false)
    }
}