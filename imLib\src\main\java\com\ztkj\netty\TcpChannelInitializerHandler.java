package com.ztkj.netty;



import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.util.CharsetUtil;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       TCPChannelInitializerHandler.java</p>
 * <p>@PackageName:     com.freddy.im.netty</p>
 * <b>
 * <p>@Description:     Channel初始化配置</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/05 07:11</p>
 * <p>@email:           <EMAIL></p>
 */
public class TcpChannelInitializerHandler extends ChannelInitializer<Channel> {

    private TcpClient tcpClient;

    public TcpChannelInitializerHandler(TcpClient tcpClient) {
        this.tcpClient = tcpClient;
    }

    @Override
    protected void initChannel(Channel channel) throws Exception {
        ChannelPipeline pipeline = channel.pipeline();
        pipeline.addLast(new StringEncoder(CharsetUtil.UTF_8));
        // 接收消息处理handler
        pipeline.addLast(new TCPReadHandler(tcpClient));
    }
}
