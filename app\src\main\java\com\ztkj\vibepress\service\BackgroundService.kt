package com.ztkj.vibepress.service

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import com.elvishew.xlog.XLog
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.geometry.SpatialReferences
import com.ztkj.baselib.ext.util.notNull
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.im.listener.IMSConnectStatusCallback
import com.ztkj.imext.bean.SingleMessage
import com.ztkj.imext.event.CEventCenter
import com.ztkj.imext.event.Events
import com.ztkj.imext.event.I_CEventListener
import com.ztkj.imext.im.IMSClientBootstrap
import com.ztkj.imext.im.MessageProcessor
import com.ztkj.imext.netty.MsgProcessor
import com.ztkj.imext.netty.TcpClientBootstrap
import com.ztkj.sensorlib.helper.SerialPortHelper
import com.ztkj.sensorlib.helper.VibeSerialHelper
import com.ztkj.sensorlib.model.GpsdataInfo
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil
import com.ztkj.vibepress.app.db.VibeDao
import com.ztkj.vibepress.app.ext.MessageStatus
import com.ztkj.vibepress.app.ext.initRTKCommand
import com.ztkj.vibepress.app.ext.messageStatus
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.CalculateUtil
import com.ztkj.vibepress.app.util.GNGGA
import com.ztkj.vibepress.app.util.HammerDTOUtils.getHammerValue
import com.ztkj.vibepress.app.util.MessageUtil
import com.ztkj.vibepress.app.util.NmeaDataToTampGpsDataEntity
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.bean.VibeMetaData
import com.ztkj.vibepress.data.model.kEnum.IMSConnectStatus
import com.ztkj.vibepress.data.model.kEnum.IMSConnectStatus.*
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.DATA_CENTER_RESPONSE
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.GNSS_ALL
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.GPGGA
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.GPS_INFO
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.REPORT_DATA
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.SENSOR_DATA
import com.ztkj.vibepress.eventViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import java.io.IOException
import java.math.RoundingMode
import javax.inject.Inject

/**
 * @CreateTime : 2023/5/17 11:05
 * <AUTHOR> AppOS
 * @Description :
 */
@AndroidEntryPoint
class BackgroundService : Service(), I_CEventListener {

    private var dataChangedCallback: ((ServiceDataType, String) -> Unit)? = null

    companion object {
        private const val TAG = "BackgroundService"

        private val EVENTS = arrayOf(
            Events.CHAT_REPORT_MESSAGE
        )

        private const val TCP_DELAY_TIME: Long = 100L
    }

    /**
     * 当前收到回复的SingleMessage
     */
    private var currentSingleMessage: SingleMessage? = null

    /**
     * Service内部处理强夯机数据存储到数据库的协程
     */
    private val serviceWriteScope = CoroutineScope(Dispatchers.IO)

    /**
     * 本地数据库发送到服务器的协程
     */
    private var roomDataScope: CoroutineScope? = null

    /**
     * 数据库Dao
     */
    @Inject
    lateinit var vibeDao: VibeDao

    private lateinit var serialPortHelper: SerialPortHelper

    private lateinit var vibeSerialHelper: VibeSerialHelper

    private val serialNumber = SerialNumberUtil.getSerial()

    private val mBinder: IBinder = MyBinder()

    private var isServiceRunning: Boolean = false

    //消息发送管理者的连接状态
    private var imsConnectStatus: IMSConnectStatus =
        CONNECT_STATE_CONNECTING

    override fun onBind(p0: Intent?): IBinder {
        return mBinder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        return super.onUnbind(intent)
    }

    inner class MyBinder : Binder() {
        fun getService(): BackgroundService {
            return this@BackgroundService
        }
    }


    /**
     * 接受硬件命令
     */
    fun receiveCommand(dataType: ServiceDataType, command: String) {
        when (dataType) {
            GNSS_ALL -> serialPortHelper.sendTxt(command)
            SENSOR_DATA -> vibeSerialHelper.sendTxt(command)
            else -> {}
        }

    }

    /**
     * 在这里添加序列化处理的原因是，如果没有添加监听，就不需要对非String对应toJson()操作
     * 节省资源开支
     */
    fun onDataChange(serviceDataType: ServiceDataType, data: Any) {
        // 调用回调函数通知数据变化
        if (data is String) {
            dataChangedCallback?.invoke(serviceDataType, data)
        } else {
            dataChangedCallback?.invoke(serviceDataType, data.toJson())
        }
    }

    // 注册回调函数
    fun setOnDataChangedCallback(callback: (ServiceDataType, String) -> Unit) {
        dataChangedCallback = callback
    }

    // 取消注册回调函数
    fun removeOnDataChangedCallback() {
        dataChangedCallback = null
    }


    override fun onCreate() {
        super.onCreate()
        CoroutineScope(Dispatchers.Main).launch {
            withContext(Dispatchers.IO) {
                println("服务启动线程: ${Thread.currentThread()}")
                try {
                    //注册事件分发中心
                    CEventCenter.registerEventListener(this@BackgroundService, EVENTS)
                    initConnection()
                    initSensorHelper()
                    startService()
                    isServiceRunning = true
                    XLog.i("串口启动状态: ${vibeSerialHelper.isOpen}")
                    "服务启动成功".toast()
                } catch (e: Exception) {
                    e.printStackTrace()
                    XLog.e("服务启动异常", e)
                } finally {
                    if (::serialPortHelper.isInitialized) {
                        serialPortHelper.initRTKCommand()
                    }
                }
            }
        }

    }

    /**
     * 启动Room To Server 服务
     */
    private fun launchSendServer() {
        roomDataScope = CoroutineScope(Dispatchers.IO)
        roomDataScope?.launch {
            reSendServer()
        }
    }

    private suspend fun reSendServer() {
        withContext(Dispatchers.IO) {
            try {
                vibeDao.getAll().collect { vibeList ->
                    vibeList.forEach { vibeMetaData ->
                        if (!isServiceRunning) {
                            cancel()
                        }
                        resendData(this, vibeMetaData)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }


    }

    /**
     * 关闭Room To Server 服务
     */
    private fun stopSendServer() {
        roomDataScope?.cancel()
        roomDataScope = null
        serviceWriteScope.cancel()
    }

    private fun removeAllTimeoutMessage() {
        IMSClientBootstrap.getInstance().removeAllTimeoutMessage()
    }


    private suspend fun resendData(coroutineScope: CoroutineScope, vibeMetaData: VibeMetaData) {
        try {
            //每次发送之前，先将这个置为空
            currentSingleMessage = null
            //发送给TCP服务器
            val async = coroutineScope.async {
                sendVibeMetaDataToNettyServer(vibeMetaData)
                onDataChange(REPORT_DATA, vibeMetaData.toJson().replace("\\", ""))
            }
            //发送消息SingleMessage的 msgId
            val msgId = async.await()
            delay(TCP_DELAY_TIME)
            val isSuccess: Boolean = msgId.equals(currentSingleMessage?.msgId)
            println("数据发送: ${currentSingleMessage?.msgId},$msgId")
            currentSingleMessage?.notNull(
                { singleMessage ->
                    if (isSuccess) {
                        //这里处理的是服务连接正常的情况，还要处理服务端挂掉的情况
                        when (singleMessage.messageStatus) {
                            MessageStatus.SUCCESS -> {
                                //发送成功，删除本地数据，继续发送
                                deleteLocalVibeMetaData(coroutineScope, vibeMetaData)
                                delay(50L)
                            }

                            MessageStatus.FAILED -> {
                                //发送失败，重新发送数据
                                delay(1 * 1000L)
                                resendData(coroutineScope, vibeMetaData)
                            }

                            MessageStatus.RESEND -> {
                                //服务端处理失败，服务端返回需要重发标识，重新发送数据
                                delay(1 * 1000L)
                                resendData(coroutineScope, vibeMetaData)
                            }

                            MessageStatus.NO_RESEND -> {
                                //服务端数据解析异常，这条数据丢弃，暂时跟发送成功一样的处理逻辑
                                deleteLocalVibeMetaData(coroutineScope, vibeMetaData)
                                delay(TCP_DELAY_TIME)
                            }

                            MessageStatus.ERROR -> {
                                XLog.d("收到了异常情况消息：${singleMessage.toJson()}")
                            }
                        }
                    } else {
                        //这里处理不相等的情况（网络异常、）
                        deleteLocalVibeMetaData(coroutineScope, vibeMetaData)
                        XLog.d("收到了不相等的消息：${singleMessage.toJson()}")
                    }
                }, {
                    //这里处理为空的情况
                    XLog.e("收到了message为空的情况")
                })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 删除本地数据
     */
    private suspend fun deleteLocalVibeMetaData(
        coroutineScope: CoroutineScope,
        vibeMetaData: VibeMetaData
    ) {
        val db = coroutineScope.async { vibeDao.delete(vibeMetaData) }
        db.await()
        currentSingleMessage = null
        println("数据发送完成,${vibeMetaData}")
    }

    private fun initConnection() {
        //TCP 服务配置
        val token = "token_$serialNumber"
        val networkConfig = CacheUtil.getNetworkConfig()
        val hosts =
            "[{\"host\":\"" + networkConfig.dataCenterHost + "\", \"port\":" + networkConfig.dataCenterIP + "}]"
        IMSClientBootstrap.getInstance()
            .init(serialNumber, token, hosts, 1, IMSConnectStatusCallbackImpl())
        //差分服务IP + 端口
        val cfhosts = "${networkConfig.diffHost} ${networkConfig.diffIP}"
        TcpClientBootstrap.getInstance().init(cfhosts)
        Log.e(TAG, "数据中心初始化完成: ")
    }

    /**
     * 初始化传感器
     * 从平 RTK定位串口：/dev/ttyWK2 RTK波特率：115200
     * 计数器串口    波特率
     */
    private fun initSensorHelper() {
        val serialConfig = CacheUtil.getSerialConfig()
        serialPortHelper =
            object : SerialPortHelper(serialConfig.rtkPath, serialConfig.rtkBaudrate.toInt()) {
                override fun onDataReceived(data: NmeaAnalysisUtil, command: String) {
                    onDataChange(GNSS_ALL, command)
                    if (command.contains("GPGGA") || command.contains("GNGGA")) {
                        onDataChange(GPGGA, command)
                        //处理业务逻辑,每隔1s向数据库插入数据
                        insertCombineDataToDatabase(data)
                        if (data.statusType > 0) {
                            appViewModel.ggaCommand.postValue(command)
                            // 向差分软件上报GPGGA，加上设备序列号后6位
                            if (command.contains("GNGGA")) {
                                val generateGNGGA = GNGGA.generateGNGGA(command)
                                XLog.d(generateGNGGA)
                                MsgProcessor.getInstance().sendMsg(generateGNGGA)
                            } else {
                                MsgProcessor.getInstance().sendMsg(command)
                            }


                        }
                    }

                }
            }

        vibeSerialHelper =
            VibeSerialHelper(serialConfig.counterPath, serialConfig.counterBaudrate.toInt()) {
                onDataChange(SENSOR_DATA, it)
                eventViewModel.currentSensorValue.postValue(it)
                CalculateUtil.processSensorData(it)
            }
        Log.e(TAG, " 串口初始化完成")
    }

    private fun postGPSInfo(gpsdataInfo: GpsdataInfo) {
        val longitude = gpsdataInfo.longitude.toDouble()
        val latitude = gpsdataInfo.latitude.toDouble()
        // 保留小数点后 6 位
        val truncatedLongitude =
            longitude.toBigDecimal().setScale(6, RoundingMode.HALF_UP).toDouble()
        val truncatedLatitude =
            latitude.toBigDecimal().setScale(6, RoundingMode.HALF_UP).toDouble()
        appViewModel.currentPosition.postValue(
            Point(
                truncatedLongitude,
                truncatedLatitude,
                SpatialReferences.getWgs84()
            )
        )
    }

    private fun insertCombineDataToDatabase(nmeaData: NmeaAnalysisUtil) {
        serviceWriteScope.launch {
            try {
                //增加状态判断，如果状态小于等于0，不插入数据
                if (nmeaData.statusType <= 0) {
                    XLog.d("状态小于等于0，不插入数据")
                    return@launch
                }
                val gpsdataInfo: GpsdataInfo = MessageUtil.getInstance().getGpsdataInfo(nmeaData)
                onDataChange(GPS_INFO, gpsdataInfo)
                postGPSInfo(gpsdataInfo)
                val body = NmeaDataToTampGpsDataEntity.transformString(
                    deviceSerialNumber = serialNumber,
                    gps = gpsdataInfo,
                    hammer = getHammerValue()
                )
                val vibeMetaData = VibeMetaData(body = body, timestamp = System.currentTimeMillis())
                vibeDao.insert(vibeMetaData)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 发送数据到后台服务器
     */
    private fun sendVibeMetaDataToNettyServer(vibeMetaData: VibeMetaData): String? {
        val singleMessage = MessageUtil.getInstance().getMsgFromLocal(serialNumber, vibeMetaData)
            ?: return null
        MessageProcessor.getInstance().sendMsg(singleMessage)
        return singleMessage.msgId
    }

    //排除获取Gps时间为负数的
    @SuppressWarnings("unused")
    private fun isValidContent(data: NmeaAnalysisUtil, content: String): Boolean {
        if (data.statusType <= 0) return false
        val split = content.split(",").toTypedArray()
        return if (split.size < 9) {
            false
        } else !split[4].startsWith("-")
    }

    override fun stopService(name: Intent?): Boolean {
        XLog.e("服务停止")
        stopService()
        return super.stopService(name)
    }

    override fun onDestroy() {
        super.onDestroy()
        XLog.e("服务销毁")
        isServiceRunning = false
        stopService()
    }


    private fun startService() {
        serialPortHelper.open()
        vibeSerialHelper.open()
        XLog.d("串口服务启动")
    }

    private fun stopService() {
        try {
            TcpClientBootstrap.getInstance().stop()
            IMSClientBootstrap.getInstance().stop()
            stopSendServer()
            serialPortHelper.close()
            vibeSerialHelper.close()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onCEvent(topic: String, msgCode: Int, resultCode: Int, obj: Any) {
        println("收到转发到应用层的消息 ${topic},msgCode: ${msgCode},resultCode: ${resultCode}, obj: ${obj.toJson()}")
        when (topic) {
            Events.CHAT_REPORT_MESSAGE -> {
                if (obj is SingleMessage) {
                    currentSingleMessage = obj
                    onDataChange(DATA_CENTER_RESPONSE, obj.content)
                }
            }
        }
    }

    inner class IMSConnectStatusCallbackImpl : IMSConnectStatusCallback {
        override fun onConnecting() {
            imsConnectStatus = CONNECT_STATE_SUCCESSFUL
        }

        override fun onConnected() {
            imsConnectStatus = CONNECT_STATE_CONNECTING
            launchSendServer()
        }

        override fun onConnectFailed() {
            imsConnectStatus = CONNECT_STATE_FAILURE
            stopSendServer()
            removeAllTimeoutMessage()
        }

    }
}