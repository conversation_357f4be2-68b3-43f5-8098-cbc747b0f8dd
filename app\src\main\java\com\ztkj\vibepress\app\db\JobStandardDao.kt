package com.ztkj.vibepress.app.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.esri.arcgisruntime.concurrent.Job
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import kotlinx.coroutines.flow.Flow

/**
 * @CreateTime : 2023/6/19 16:44
 * <AUTHOR> AppOS
 * @Description :
 */
@Dao
interface JobStandardDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(jobStandardEntity: JobStandardEntity): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(jobStandardList: List<JobStandardEntity>)


    @Query("SELECT * FROM JobStandardEntity LIMIT 1")
    fun getJobStandard(): JobStandardEntity?

    @Query("SELECT * FROM JobStandardEntity")
    fun getJobStandardList(): Flow<List<JobStandardEntity>>


    @Query("SELECT * FROM JobStandardEntity WHERE workType = :workType")
    fun getJobStandardsByWorkType(workType: Int): List<JobStandardEntity>


    @Query("SELECT * FROM JobStandardEntity WHERE uID = :uID")
    suspend fun getJobStandardByUid(uID: Long): JobStandardEntity?

    @Query("DELETE FROM JobStandardEntity WHERE uID = :uID")
    suspend fun deleteByUid(uID: Long)

    @Query("DELETE FROM JobStandardEntity")
    suspend fun deleteAll()
}