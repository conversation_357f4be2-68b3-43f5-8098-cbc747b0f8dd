package com.ztkj.vibepress.data.reposity

import com.ztkj.vibepress.app.db.TampDeviceDao
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.data.model.bean.ApiResponse
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * @CreateTime : 2023/6/5 20:20
 * <AUTHOR> AppOS
 * @Description :
 */
@Singleton
class TampDeviceRepository @Inject constructor(private val tampDeviceDao: TampDeviceDao) {


    suspend fun insert(tampDeviceEntity: TampDeviceEntity) {
        tampDeviceDao.insertTampDevice(tampDeviceEntity)
    }

    suspend fun getTampEntity(): TampDeviceEntity? {
        return tampDeviceDao.getTampDevice()
    }

    fun getTampEntityFlow(): Flow<TampDeviceEntity?> {
        return tampDeviceDao.getTampDeviceFlow()
    }

    suspend fun updateTampDeviceEntity(tampDeviceEntity: TampDeviceEntity): ApiResponse<Nothing> {
        return apiService.updateTampDeviceEntity(tampDeviceEntity)
    }
}