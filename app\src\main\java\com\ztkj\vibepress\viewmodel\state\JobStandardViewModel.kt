package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.ztkj.vibepress.app.ext.showToast
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.reposity.JobStandardRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import java.lang.NumberFormatException
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * @CreateTime : 2023/6/19 16:48
 * <AUTHOR> AppOS
 * @Description :
 */
@HiltViewModel
class JobStandardViewModel @Inject constructor(private val jobStandardRepository: JobStandardRepository) :
    ViewModel() {

    val jobStandardError = MutableLiveData(false)

    suspend fun insert(jobStandardMap: Map<String, String>?): Long =
        suspendCoroutine { continuation ->
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    val gson = Gson()
                    val dataJson = gson.toJson(jobStandardMap)
                    val jobStandardEntity = gson.fromJson(dataJson, JobStandardEntity::class.java)
                    if (jobStandardEntity.checkValidData()) {
                        val uId = jobStandardRepository.insert(jobStandardEntity)
                        continuation.resume(uId) // 返回 uId
                    } else {
                        jobStandardError.postValue(true)
//                        continuation.resume(-1L)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    showToast("参数异常,请检查!!!")
                }
            }
        }

    private fun checkValidData(jobStandardEntity: JobStandardEntity) {

    }

    fun insertAll(entities: List<JobStandardEntity>) {
        viewModelScope.launch(Dispatchers.IO) {
            for (entity in entities) {
                entity.serialNum = SerialNumberUtil.getSerial()
            }
            jobStandardRepository.insertAll(entities)
        }
    }


    suspend fun getJobStandardEntityByUid(uId: Long): JobStandardEntity? {
        return jobStandardRepository.getJobStandardByUid(uId)
    }

    fun updateJobStandard(jobStandardEntity: JobStandardEntity) {
        viewModelScope.launch(Dispatchers.IO) {
            jobStandardRepository.insert(jobStandardEntity)
        }
    }

    fun getJobStandardList(): Flow<List<JobStandardEntity>> {
        return jobStandardRepository.getJobStandardList()
    }

    fun deleteEntityByUid(uId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            jobStandardRepository.deleteJobStandardByUid(uId)
        }
    }

    fun deleteAll() {
        viewModelScope.launch(Dispatchers.IO) {
            jobStandardRepository.deleteAll()
        }
    }


}