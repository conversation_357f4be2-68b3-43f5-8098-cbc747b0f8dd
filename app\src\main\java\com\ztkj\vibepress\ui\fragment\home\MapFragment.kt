package com.ztkj.vibepress.ui.fragment.home

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.elvishew.xlog.XLog
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.geometry.SpatialReferences
import com.google.gson.Gson
import com.ztkj.app.mapcore.BaseMapHelper
import com.ztkj.app.mapcore.GeoMapManager
import com.ztkj.app.mapcore.GeoMapManager.MapType.Map2D
import com.ztkj.baselib.ext.parseState
import com.ztkj.baselib.ext.util.notNull
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.mapext.loadTdtImgLayer
import com.ztkj.sensorlib.model.GpsdataInfo
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.delayedLoading
import com.ztkj.vibepress.app.ext.showMessage
import com.ztkj.vibepress.app.ext.showToast
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants.MAP.Companion.WFS_TAMPING_TABLE_PREFIX
import com.ztkj.vibepress.data.Constants.MAP.Companion.WFS_TAMPING_URL
import com.ztkj.vibepress.data.Constants.MAP.Companion.WMS_DESIGN_POINT_TABLE_NAME
import com.ztkj.vibepress.data.Constants.MAP.Companion.WMS_DESIGN_POINT_URL
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.GPS_INFO
import com.ztkj.vibepress.databinding.FragmentMapBinding
import com.ztkj.vibepress.eventViewModel
import com.ztkj.vibepress.service.BackgroundService
import com.ztkj.vibepress.viewmodel.request.RequestMapViewModel
import com.ztkj.vibepress.viewmodel.state.HomeViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MapFragment : BaseFragment<HomeViewModel, FragmentMapBinding>() {

    private val requestMapViewModel: RequestMapViewModel by viewModels()

    /**
     * MapView Helper Tools
     */
    private lateinit var helper: BaseMapHelper

    private var boundService: BackgroundService? = null

    //上一次的位置
    private var lastPosition: Point? = null


    override fun initView(savedInstanceState: Bundle?) {
        //databinding viewmodel
        binding.vm = mViewModel
        binding.click = ProxyClick()
        //init helper
        helper = GeoMapManager.buildHelper(Map2D)

        mViewModel.show()

        initMap()

    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        /*  helper.loadVpFeatureLayer("http://218.77.59.2:8249/arcgis/rest/services/features/clhd/MapServer/0")
          helper.setMapCenter(Point(119.6770739000001, 25.934448700000075))*/
//        helper.setMapCenter(Point(85.96392662, 30.11705178), 50.0)
        val center: Point =Point(112.96072247892924	, 27.287562182085438)
        helper.setMapCenter(center, 10.0)
        delayedLoading(1000L) {
            helper.setMapCenter(center, 30.0)
            //加载WMS 设计点图层
            helper.loadWmsLayer(WMS_DESIGN_POINT_URL, listOf(WMS_DESIGN_POINT_TABLE_NAME))
            //加载夯点图层
            val deptId = appViewModel.tampDeviceEntity.value?.deptId
            helper.loadRefreshWmsLayer(
                WMS_DESIGN_POINT_URL,
                listOf(WFS_TAMPING_TABLE_PREFIX + deptId),
                5000L,
                "status=1"
            )

        }
        helper.drawHammer(center,R.drawable.dcm,0.0f)
        //TODO 该用其他方式获取位置
        /*        delayedLoading {
                    bindService()
                }*/

        delayedLoading(10 * 1000L) {
            startPeriodicWfsLoading()
        }

    }

    /**
     * 加载最近的夯点
     */
    private fun loadNearestTampingPoint() {
        val deptId = appViewModel.tampDeviceEntity.value?.deptId
        helper.loadNearestTampingPoint(WFS_TAMPING_URL, WFS_TAMPING_TABLE_PREFIX + deptId) {
            it.notNull({ attributes ->
                val hitCount = attributes["hit_count"].toString()
                val stakeNumber = attributes["stake_code"].toString()
                if (stakeNumber.isBlank()) {
                    eventViewModel.currentStakeNumber.value = ""
                } else {
                    eventViewModel.currentStakeNumber.value = stakeNumber
                }

                eventViewModel.currentHitCount.value = hitCount
            }, {
                eventViewModel.currentHitCount.value = "0"
            })
        }
    }

    /**
     * 加载施工引导点
     */
    private fun loadWfsLayer() {
//        val center = appViewModel.currentPosition.value
        val center: Point =Point(112.96072247892924	, 27.287562182085438)
        if (center != null) {
            helper.loadWfsLayer(
                WFS_TAMPING_URL, WMS_DESIGN_POINT_TABLE_NAME, center.x, center.y
            ) {
/*            it.notNull({ attributes ->
                //设置当前作业的桩号
                val stakeNumber = attributes["stake_code"].toString()
                eventViewModel.currentStakeNumber.value = stakeNumber
            }, {
                eventViewModel.currentStakeNumber.value = ""
            })*/

            }
        }

    }


    /**
     * 定时加载夯点图层
     */
    private fun startPeriodicWfsLoading() {
        lifecycleScope.launch {
            while (isActive) {
                loadWfsLayer()
//                loadNearestTampingPoint()
                delay(10 * 1000)
            }
        }
    }


    private fun initMap() {
        //init map
        helper.init(binding.mapView)
        //load TianDiTu vector layer
        helper.loadTdtImgLayer()

        initMapTouchListener()
    }

    private fun initMapTouchListener() {
        helper.setMapOnTouchCallback { _, layerName, bbox ->
            requestMapViewModel.findFeatures(layerName, bbox)
        }
    }

    override fun createObserver() {
        super.createObserver()
        requestMapViewModel.featureResult.observe(this@MapFragment) { resultState ->
            parseState(resultState, {
                helper.loadNearbyDesignPoint(it)
                XLog.d(it.toJson())
            }, {
                it.printStackTrace()
                XLog.d(it.message)
            })
        }

        appViewModel.currentPosition.observeInFragment(this@MapFragment) {
            if (lastPosition?.x != it.x || lastPosition?.y != it.y) {
                helper.drawHammer(it, R.drawable.dcm, 0.0f)
                lastPosition = it
            }
        }

        /**
         * 当前的夯点号
         */
        eventViewModel.currentStakeNumber.observeInFragment(this@MapFragment) {
            //加载夯点图层
            val deptId = appViewModel.tampDeviceEntity.value?.deptId
            helper.loadNearbyTampingPointList(WFS_TAMPING_URL, WFS_TAMPING_TABLE_PREFIX + deptId) {
                for (queryResult in it) {
                    val attributes = queryResult.attributes
                    val stakeNumber = attributes["stake_code"].toString()
                    if (stakeNumber == eventViewModel.currentStakeNumber.value) {
                        helper.drawHighlightPoint(
                            eventViewModel.currentHitCount.value,
                            queryResult.geometry
                        )
                        return@loadNearbyTampingPointList
                    }
                }
            }
            /*            helper.loadNearestTampingPoint(WFS_TAMPING_URL, WFS_TAMPING_TABLE_PREFIX + deptId) {
                            it.notNull({ result ->
                                XLog.tag("MapFragment").d(result.toJson())
                            }, {
                                XLog.tag("MapFragment").d("夯点为空")
                            })
                        }*/
        }
    }


    private fun handleServiceData(serviceDataType: ServiceDataType, data: String) {
        if (serviceDataType == GPS_INFO) {
            try {
                val gson = Gson()
                val gpsdataInfo = gson.fromJson(data, GpsdataInfo::class.java)
                val point = Point(
                    gpsdataInfo.longitude.toDouble(),
                    gpsdataInfo.latitude.toDouble(),
                    SpatialReferences.getWgs84()
                )
                //TODO 夯机暂时不考虑角度问题
                helper.drawHammer(point, R.drawable.dcm, 0.0f)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun bindService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().bindService(intent, mServiceConnection, 0)
    }

    private fun unBindService() {
        boundService?.removeOnDataChangedCallback()
        boundService = null
        requireContext().unbindService(mServiceConnection)
    }


    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                boundService?.setOnDataChangedCallback { serviceDataType, data ->
                    handleServiceData(serviceDataType, data)
                }
            }

        }

        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
        }

    }

    inner class ProxyClick {
        fun setting() {
        }

        fun zoomIn() {
            helper.mapZoomIn()
        }

        fun zoomOut() {
            helper.mapZoomOut()
        }
    }

}