package com.ztkj.vibepress.app.event

import com.esri.arcgisruntime.geometry.Point
import com.kunminx.architecture.ui.callback.UnPeekLiveData
import com.ztkj.baselib.base.appContext
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.callback.livedata.event.EventLiveData
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.SettingUtil
import com.ztkj.vibepress.data.model.bean.HammerDataDTO
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.LoginClient
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.data.model.kEnum.AppType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType

/**
 * @CreateTime : 2022/10/13 15:31
 * <AUTHOR> AppOS
 * @Description : APP全局的ViewModel，可以存放公共数据，当他数据改变时，所有监听他的地方都会收到回调,也可以做发送消息
 * 全局可使用的 地理位置信息，账户信息,App的基本配置等等，
 */
class AppViewModel : BaseViewModel() {

    /**
     * App的账户信息
     */
    var userInfo: UnPeekLiveData<LoginClient> =
        UnPeekLiveData.Builder<LoginClient>().setAllowNullValue(true).create()

    /**
     * App主题颜色 中大型项目不推荐以这种方式改变主题颜色，比较繁琐耦合，且容易有遗漏某些控件没有设置主题色
     */
    var appColor = EventLiveData<Int>()

    /**
     * App 列表动画
     */
    var appAnimation = EventLiveData<Int>()

    /**
     * 接收到的GPGGA数据,初始化为1s一条
     */
    var ggaCommand: UnPeekLiveData<String> =
        UnPeekLiveData.Builder<String>().setAllowNullValue(true).create()

    var hammerDataDTO = EventLiveData<HammerDataDTO>()

    var appType = EventLiveData<AppType>()

    var currentWorkType = EventLiveData<TampWorkType>()

    var currentPosition = EventLiveData<Point>()

    /**
     * 当前的作业标准
     */
    var jobStandardEntity = EventLiveData<JobStandardEntity>()

    var tampDeviceEntity = EventLiveData<TampDeviceEntity>()

    var hammerHeight = EventLiveData<Float>()

    init {
        // 默认值保存的账户信息，没有登陆过则为null
        userInfo.value = CacheUtil.getUser()
        //默认值颜色
        appColor.value = SettingUtil.getColor(appContext)
        //初始化列表动画
        appAnimation.value = SettingUtil.getListMode()

        hammerDataDTO.value = HammerDataDTO()


        appType.value = CacheUtil.getAppType()

        jobStandardEntity.value = CacheUtil.getCurrentJobStandard()

        currentWorkType.value = CacheUtil.getCurrentWorkType()

    }
}