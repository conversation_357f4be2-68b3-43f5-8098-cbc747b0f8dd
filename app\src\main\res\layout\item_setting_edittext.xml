<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title"
        tools:text="标题:"
        android:gravity="end|center_vertical"
        android:textColor="@color/white"
        android:paddingEnd="5dp"
        android:maxLines="1"
        android:paddingStart="0dp"
        android:textSize="16sp"
        android:layout_width="120dp"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/edittext"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:maxLines="1"
        android:textSize="16sp"
        android:inputType="numberDecimal"
        android:background="@drawable/shape_setting_item_border"
        android:hint="@string/please_enter"
        android:textColor="@color/white"
        android:textColorHint="@color/white"
        android:imeOptions="actionNext" />
</LinearLayout>