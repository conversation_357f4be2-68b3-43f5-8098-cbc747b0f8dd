package com.ztkj.vibepress.app.ext

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.util.*

/**
 * @CreateTime : 2023/1/11 10:56
 * <AUTHOR> AppOS
 * @Description :
 */

// 字符串日期 获取想要格式的日期格式，栗子："2017—10-10 10:10:10"
@SuppressLint("SimpleDateFormat")
fun Date.getTime4String(time: String): String {
    if (time.isEmpty()) return ""
    //代转日期的字符串格式(输入的字符串格式)
    val inputsdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    //获取想要的日期格式(输出的日期格式)
    val outputsdf = SimpleDateFormat("yyyy-MM-dd")
    val date: Date = inputsdf.parse(time) as Date
    return outputsdf.format(date)
}