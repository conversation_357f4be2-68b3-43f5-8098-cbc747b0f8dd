package com.ztkj.vibepress.app.util

import com.google.gson.Gson
import com.ztkj.sensorlib.model.GpsdataInfo
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.bean.HammerDataDTO
import com.ztkj.vibepress.data.model.bean.TampGpsDataEntity

/**
 * @CreateTime : 2023/5/18 16:42
 * <AUTHOR> AppOS
 * @Description : RTK读取的NMEA数据和夯锤传感器读取到的数据转换器
 */

object NmeaDataToTampGpsDataEntity {

    @JvmStatic
    fun transform(
        deviceSerialNumber: String,
        gps: GpsdataInfo,
        hammer: HammerDataDTO
    ): TampGpsDataEntity {
        return TampGpsDataEntity(
            serialNum = deviceSerialNumber,
            sendTimestamp = gps.gpsTime,
            carLatitude = gps.latitude,
            carLongitude = gps.longitude,
            altitude = gps.altitude,
            gpsStatus = gps.solution,
            satelliteNum = gps.satnum,
            differentialDelay = gps.diffdelay ?: "",
            differentialStation = gps.diffstation ?: "",
            ellipsoidalHeight = gps.ellipsoid,
            orientation = gps.devicedir,
            speed = gps.speed,
            hammerLatitude = gps.latitude,
            hammerLongitude = gps.longitude,
            sensorValue = hammer.sensorValue,
            hammerHeight = hammer.hammerHeight,
            hammerState = hammer.hammerState,
            tensionState = hammer.tensionState,
            tensionChange = hammer.tensionChange,
            tensionValue = hammer.tensionValue,
            hammerRadius = appViewModel.jobStandardEntity.value?.hammerRadius ?: 0.0,
            hammerWeight = appViewModel.jobStandardEntity.value?.hammerWeight ?: 0.0,
            jobStandardId = appViewModel.jobStandardEntity.value?.id,
            calibration = CacheUtil.getHammerHeightRatio()
        )
    }

    @JvmStatic
    fun transformString(
        deviceSerialNumber: String,
        gps: GpsdataInfo,
        hammer: HammerDataDTO
    ): String {
        return Gson().toJson(transform(deviceSerialNumber, gps, hammer))
    }
}