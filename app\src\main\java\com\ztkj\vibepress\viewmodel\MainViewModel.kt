package com.ztkj.vibepress.viewmodel

import android.util.Log
import androidx.lifecycle.viewModelScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.vibepress.data.model.bean.VibeMetaData
import com.ztkj.vibepress.data.reposity.VibeDataRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @CreateTime : 2023/5/16 11:59
 * <AUTHOR> AppOS
 * @Description :
 */
//TODO 这里的MainViewModel 应该设置为全局
@HiltViewModel
class MainViewModel @Inject internal constructor(private val vibeDataRepository: VibeDataRepository): BaseViewModel() {

    private val vibeMetaDataList: Flow<List<VibeMetaData>> = vibeDataRepository.getAllVibeData()

    fun show() {
        viewModelScope.launch {
            vibeMetaDataList.collect() { vibeMetaDataList ->
                vibeMetaDataList.forEach { vibeMetaData ->
                    Log.e(TAG, "删除的数据: $vibeMetaData" )
                    delay(1000L)
                    vibeDataRepository.delete(vibeMetaData)
                }
            }
        }
    }

    fun generateData() {
        viewModelScope.launch {
            for (i in 0 until 10000){
                vibeDataRepository.insertVibeData(i.toString())
                delay(50L)
            }
        }

    }
}