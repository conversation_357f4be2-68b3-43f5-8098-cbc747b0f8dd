package com.ztkj.vibepress.app.widget.customview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ztkj.vibepress.databinding.LayoutElevationViewBinding

/**
 * @CreateTime : 2023/8/14 16:30
 * <AUTHOR> AppOS
 * @Description :
 */
class ElevationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val binding: LayoutElevationViewBinding

    init {
        binding = LayoutElevationViewBinding.inflate(LayoutInflater.from(context), this, true)
    }


    fun setElevationText(text: String) {
        binding.textView.text = text
    }

}