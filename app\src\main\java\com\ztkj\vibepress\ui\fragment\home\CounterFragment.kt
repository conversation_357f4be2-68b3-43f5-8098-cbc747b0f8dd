package com.ztkj.vibepress.ui.fragment.home

import android.content.res.ColorStateList
import android.os.Bundle
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.databinding.FragmentCounterBinding
import com.ztkj.vibepress.viewmodel.state.CounterStateViewModel

class CounterFragment : BaseFragment<CounterStateViewModel, FragmentCounterBinding>() {

    override fun initView(savedInstanceState: Bundle?) {
        // 设置DataBinding
        binding.vm = mViewModel
        binding.click = ProxyClick()

        // 初始化时间显示
        mViewModel.resetTimer()

        // 初始化UI状态
        updateModeButton()
        updateControlButton()
    }

    override fun createObserver() {
        super.createObserver()

        // 观察Toast消息
        mViewModel.toastMessage.observe(this, Observer { message ->
            message?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        })

        // 倒计时输入变化现在通过DataBinding的afterTextChanged自动处理

        // 观察模式变化
        mViewModel.isCountUpMode.observe(this, Observer {
            updateModeButton()
        })

        // 观察运行状态变化
        mViewModel.isRunning.observe(this, Observer {
            updateControlButton()
        })
    }

    /**
     * 更新模式按钮样式
     */
    private fun updateModeButton() {
        val isCountUp = mViewModel.isCountUpMode.value ?: true
        val primaryColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
        val backgroundColor = ContextCompat.getColor(requireContext(), R.color.backgroundeee)
        val whiteColor = ContextCompat.getColor(requireContext(), R.color.white)

        if (isCountUp) {
            // 正数模式：蓝色背景
            binding.btnToggleMode.backgroundTintList = ColorStateList.valueOf(primaryColor)
            binding.btnToggleMode.setTextColor(whiteColor)
        } else {
            // 倒数模式：灰色背景
            binding.btnToggleMode.backgroundTintList = ColorStateList.valueOf(backgroundColor)
            binding.btnToggleMode.setTextColor(primaryColor)
        }
    }

    /**
     * 更新控制按钮样式
     */
    private fun updateControlButton() {
        val isRunning = mViewModel.isRunning.value ?: false
        val primaryColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
        val redColor = ContextCompat.getColor(requireContext(), R.color.red)

        if (isRunning) {
            // 运行中：显示停止按钮
            binding.btnStartStop.backgroundTintList = ColorStateList.valueOf(redColor)
            binding.btnStartStop.setIconResource(R.drawable.ic_stop)
        } else {
            // 停止中：显示开始按钮
            binding.btnStartStop.backgroundTintList = ColorStateList.valueOf(primaryColor)
            binding.btnStartStop.setIconResource(R.drawable.ic_play)
        }
    }

    /**
     * 点击事件代理类
     */
    inner class ProxyClick {

        /**
         * 切换计时器模式
         */
        fun toggleMode() {
            mViewModel.toggleMode()
        }

        /**
         * 开始/停止计时器
         */
        fun startStopTimer() {
            if (mViewModel.isRunning.value == true) {
                mViewModel.stopTimer()
            } else {
                mViewModel.startTimer()
            }
        }


    }
}