package com.ztkj.baselib.util;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工具类
 */
public class ThreadPoolUtils {

    /**
     * 线程池代理
     */
    static ThreadPoolProxy mNormalThreadPoolProxy;
    /**
     * 核心线程数(根据AsynTask源码)
     */
    static int mNormalThreadCorePoolSize = CpuHelp.getNumCores() + 1;
    /**
     * 最大线程数
     */
    static int mNormalMaximumPoolSize = CpuHelp.getNumCores() * 2 + 1;

    public static ThreadPoolProxy getNormalPool() {
        if (mNormalThreadPoolProxy == null) {
            synchronized (ThreadPoolUtils.class) {
                if (mNormalThreadPoolProxy == null) {
                    mNormalThreadPoolProxy = new ThreadPoolProxy(mNormalThreadCorePoolSize, mNormalMaximumPoolSize);
                }
            }
        }
        return mNormalThreadPoolProxy;
    }

    public static class ThreadPoolProxy {
        private ThreadPoolExecutor mExecutor;
        /**
         * 核心池的大小
         */
        private int mCorePoolSize;
        /**
         * 线程最大线程数
         */
        private int mMaximumPoolSize;

        public ThreadPoolProxy(int corePoolSize, int maximumPoolSize) {
            mMaximumPoolSize = maximumPoolSize;
            mCorePoolSize = corePoolSize;
        }

        public void initThreadPoolExecutor() {
            //双重检查加锁 : 只有第一次实例化的时候才启用同步机制,提高了性能
            if (mExecutor == null || mExecutor.isShutdown() || mExecutor.isTerminated()) {
                synchronized (ThreadPoolProxy.class) {
                    if (mExecutor == null || mExecutor.isShutdown() || mExecutor.isTerminated()) {
                        //保持存活时间，当线程数大于corePoolSize的空闲线程能保持的最大时间。
                        long keepAliveTime = 1;
                        //时间单位
                        TimeUnit unit = TimeUnit.MILLISECONDS;
                        //任务队列，主要用来存储已经提交但未被执行的任务
                        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue();
                        //线程工厂，用来创建线程池中的线程，通常用默认的即可
                        ThreadFactory threadFactory = Executors.defaultThreadFactory();

                        //通常叫做拒绝策略，1、在线程池已经关闭的情况下 2、任务太多导致最大线程数和任务队列已经饱和，无法再接收新的任务 。
                        // 在上面两种情况下，只要满足其中一种时，在使用execute()来提交新的任务时将会拒绝，而默认的
                        // 拒绝策略是抛一个RejectedExecutionException异常
                        RejectedExecutionHandler handler = new ThreadPoolExecutor.DiscardPolicy();
                        mExecutor = new ThreadPoolExecutor(mCorePoolSize, mMaximumPoolSize,
                                keepAliveTime, unit, workQueue, threadFactory, handler);
                    }
                }
            }
        }

        /**
         * 提交任务,有返回值
         * Future对象是异步任务执行完成之后的结果
         */
        public Future submit(Runnable task) {
            initThreadPoolExecutor();
            Future<?> future = mExecutor.submit(task);
            return future;
        }

        /**
         * 执行任务
         */
        public void execute(Runnable task) {
            initThreadPoolExecutor();
            mExecutor.execute(task);
        }

        /**
         * 移除任务
         */
        public void remove(Runnable task) {
            initThreadPoolExecutor();
            mExecutor.remove(task);
        }
    }


}
