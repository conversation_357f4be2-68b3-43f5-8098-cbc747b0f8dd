package com.ztkj.vibepress.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.request
import com.ztkj.baselib.state.ResultState
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.data.model.bean.JobStandardEntity

/**
 * @CreateTime : 2023/7/4 16:14
 * <AUTHOR> AppOS
 * @Description :
 */
class RequestJobStandardViewModel : BaseViewModel() {

    var addOrUpdateResult = MutableLiveData<ResultState<String>>()

    var jobStandardListResult = MutableLiveData<ResultState<List<JobStandardEntity>>>()

    var deleteResult = MutableLiveData<ResultState<Nothing>>()

    fun addOrUpdateJobStandard(jobStandardEntity: JobStandardEntity) {
        request(
            { apiService.updateJobStandard(jobStandardEntity) },
            addOrUpdateResult,
            true,
            "加载中..."
        )
    }

    fun getJobStandardList() {
        request(
            { apiService.getJobStandardList(SerialNumberUtil.getSerial()) },
            jobStandardListResult
        )
    }

    fun deleteJobStandardById(id: String?) {
        if (id == null) {
            return
        }
        request(
            { apiService.deleteJobStandard(id) },
            deleteResult
        )
    }
}