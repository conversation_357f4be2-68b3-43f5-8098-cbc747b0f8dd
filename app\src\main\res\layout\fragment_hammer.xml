<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable

            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.HammerStateViewModel" />

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.home.HammerFragment.ProxyClick" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/animation_fragment_bg"
        android:paddingTop="?attr/actionBarSize"
        tools:context=".ui.fragment.home.HammerFragment">

        <com.ztkj.vibepress.app.widget.customview.HammerView
            android:id="@+id/hammerView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/shape_solid_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:minWidth="100dp"
            android:layout_width="wrap_content"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content"
            android:minHeight="100dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginTop="5dp"
                android:textSize="32sp"
                android:text='@{vm.realHitCount +"/"+ vm.requireHitCount}' />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前击数"
                android:layout_marginTop="5dp"
                android:textSize="16sp"
                android:textColor="#333333" />

            <TextView
                android:layout_marginTop="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text='@{"夯点号:\n"+ vm.currentStake}'
                android:textSize="16sp"
                android:textColor="#333333" />

            <TextView
                android:layout_marginTop="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text='@{"锤高: "+ vm.hammerHeight}'
                android:textSize="13sp"
                android:layout_marginBottom="10dp"
                android:textColor="#333333" />

        </LinearLayout>

        <LinearLayout
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:orientation="vertical"
            android:layout_width="160dp"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvJobType"
                style="@style/MainDeviceAttributeTextView"
                android:textColor="#1BCAD6"
                android:onClick="@{ () -> click.changeJobStandard()}"
                android:textSize="18sp"
                android:drawablePadding="10dp"
                android:drawableEnd="@mipmap/arrow_down"
                android:text="作业名称" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tampingType"
                style="@style/MainDeviceAttributeTextView"
                android:text='@{"夯机类型: "+ vm.workTypeString}' />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvHammerWeight"
                style="@style/MainDeviceAttributeTextView"
                android:text='@{"夯锤重量: " + vm.hammerWeight +"t"}' />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/MainDeviceAttributeTextView"
                android:text='@{"夯  击  能: " + vm.requireEnergy + "KN·m"}' />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHammerRadius"
            android:visibility="invisible"
            style="@style/MainDeviceAttributeTextView"
            android:drawableStart="@mipmap/ic_hammer_radius"
            android:text='@{"夯锤半径: "+ vm.hammerRadius +"m"}'
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>