package com.ztkj.baselib.base.activity

import android.view.View
import androidx.viewbinding.ViewBinding
import com.ztkj.baselib.ext.inflateBindingWithGeneric
import com.ztkj.baselib.base.viewmodel.BaseViewModel

/**
 * @CreateTime : 2022/10/12 10:47
 * <AUTHOR> AppOS
 * @Description : 包含 ViewModel 和 ViewBinding ViewModelActivity基类，把ViewModel 和 ViewBinding 注入进来了
 * 需要使用 ViewBinding 的清继承它
 */
abstract class BaseVmVbActivity<VM : BaseViewModel, VB : ViewBinding> : BaseVmActivity<VM>() {

    override fun layoutId(): Int = 0

    lateinit var binding: VB

    /**
     * 创建DataBinding
     */
    override fun initDataBind(): View? {
        binding = inflateBindingWithGeneric(layoutInflater)
        return binding.root

    }
}