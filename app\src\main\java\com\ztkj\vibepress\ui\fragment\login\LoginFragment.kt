package com.ztkj.vibepress.ui.fragment.login

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.kEnum.AppType
import com.ztkj.vibepress.databinding.FragmentLoginBinding

class LoginFragment : BaseFragment<BaseViewModel, FragmentLoginBinding>() {


    companion object {
        private val tabTitles = arrayOf("联机", "单机")
    }


    override fun initView(savedInstanceState: Bundle?) {
        initViewPager2()
        initTabLayout()
        setDefaultTabItem()
    }

    private fun setDefaultTabItem() {
        when (appViewModel.appType.value) {
            AppType.ONLINE -> binding.viewPager2.currentItem = 0
            AppType.OFFLINE -> binding.viewPager2.currentItem = 1
            null -> {
                appViewModel.appType.value = AppType.OFFLINE
            }
        }
    }

    private fun initTabLayout() {
        TabLayoutMediator(binding.tabLayout, binding.viewPager2) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()
    }

    private fun initViewPager2() {
        binding.viewPager2.run {
            adapter = SimpleFragmentPagerAdapter(requireActivity())
            /*  registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                  override fun onPageScrollStateChanged(state: Int) {
                      super.onPageScrollStateChanged(state)

                  }

                  override fun onPageScrolled(
                      position: Int,
                      positionOffset: Float,
                      positionOffsetPixels: Int
                  ) {
                      super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                  }

                  override fun onPageSelected(position: Int) {
                      super.onPageSelected(position)
                  }
              })*/
        }
    }

    private inner class SimpleFragmentPagerAdapter constructor(fm: FragmentActivity) :
        FragmentStateAdapter(fm) {
        private val fragment = arrayOf(OnlineLoginFragment(), OfflineLoginFragment())
        override fun getItemCount(): Int = fragment.size

        override fun createFragment(position: Int): Fragment {
            if (position > fragment.size) {
                throw IllegalArgumentException("数组异常")
            }
            return fragment[position]
        }
    }

}