package com.ztkj.vibepress.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.ztkj.app.mapcore.entity.GeoJsonModel
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.requestNoCheck
import com.ztkj.baselib.state.ResultState
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.app.network.geoService

/**
 * @CreateTime : 2023/8/10 15:40
 * <AUTHOR> AppOS
 * @Description :
 */
class RequestMapViewModel : BaseViewModel() {

    var featureResult = MutableLiveData<ResultState<GeoJsonModel>>()


    fun findFeatures(layerName: String, bbox: String) {
        val buildWmsFeatureMap = buildWmsFeatureMap(layerName, bbox)
        requestNoCheck(
            { geoService.geoWmsFeature(buildWmsFeatureMap) },
            featureResult
        )

    }


    private fun buildWmsFeatureMap(layerName: String, bbox: String): Map<String, String> {
        val map: MutableMap<String, String> = HashMap()
        map["SERVICE"] = "WMS"
        map["VERSION"] = "1.1.1"
        map["REQUEST"] = "GetFeatureInfo"
        map["FORMAT"] = "image/png"
        map["TRANSPARENT"] = "true"
        map["QUERY_LAYERS"] = layerName
        map["LAYERS"] = layerName
        map["exceptions"] = "application/vnd.ogc.se_inimage"
        map["INFO_FORMAT"] = "application/json"
        map["FEATURE_COUNT"] = "50"
        map["X"] = "50"
        map["Y"] = "50"
        map["SRS"] = "EPSG:4326"
        map["WIDTH"] = "101"
        map["HEIGHT"] = "101"
        map["BBOX"] = bbox
        return map
    }
}