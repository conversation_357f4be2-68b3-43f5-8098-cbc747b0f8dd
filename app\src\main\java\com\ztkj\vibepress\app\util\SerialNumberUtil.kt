package com.ztkj.vibepress.app.util

import android.annotation.SuppressLint
import android.util.Log
import com.ztkj.vibepress.appViewModel

/**
 * @CreateTime : 2023/5/17 14:50
 * <AUTHOR> AppOS
 * @Description :
 */
object SerialNumberUtil {

    //SN
    @SuppressLint("PrivateApi")
    fun getSerial(): String {
        if (CacheUtil.isLogin()){
            return CacheUtil.getUser()?.user_name ?: ""
        }
//        return "110DS98392800052"
        //android 6 ,9 getsn
        var serial: String? = null
        try {
            val c = Class.forName("android.os.SystemProperties")
            val get = c.getMethod("get", String::class.java)
            serial = get.invoke(c, "ro.serialno") as String
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("setSerialNumber", "获取设备序列号失败")
        }
        //去掉从平平板获取到的设备SN码前面的`APOLLO` eg: APOLLO1026321825
        return serial?.replace("APOLLO", "") ?: ""    //Build.SERIAL
    }
}