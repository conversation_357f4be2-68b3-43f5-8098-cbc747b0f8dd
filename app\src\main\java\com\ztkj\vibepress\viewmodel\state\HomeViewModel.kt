package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.viewModelScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
//import com.ztkj.vibepress.app.db.VibeDao
import com.ztkj.vibepress.data.model.bean.VibeMetaData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @CreateTime : 2023/4/23 15:07
 * <AUTHOR> AppOS
 * @Description :
 */
@HiltViewModel
class HomeViewModel @Inject constructor() : BaseViewModel() {

//    @Inject lateinit var vibeDao: VibeDao

    fun show() {
        viewModelScope.launch {


        }
    }

    fun sendToServer(vibeMetaData: VibeMetaData): Boolean {
        println(vibeMetaData)
        return true
    }

}