package com.ztkj.vibepress.data.reposity

import com.ztkj.vibepress.app.db.VibeDao
import com.ztkj.vibepress.data.model.bean.VibeMetaData
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * @CreateTime : 2023/5/16 12:00
 * <AUTHOR> AppOS
 * @Description :
 */
@Singleton
class VibeDataRepository @Inject constructor(private val vibeDao: VibeDao){

    fun getAllVibeData(): Flow<List<VibeMetaData>> = vibeDao.getAll()

    suspend fun insertVibeData(body: String) {
//        val vibeMetaData = VibeMetaData(body = body, timestamp = System.currentTimeMillis())
//        vibeDao.insert(vibeMetaData)
    }

    suspend fun delete(vibeMetaData: VibeMetaData) {
        return  vibeDao.delete(vibeMetaData)
    }

    suspend fun insertCombineData() {

    }



}