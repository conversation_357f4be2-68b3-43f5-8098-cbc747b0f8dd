<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CardViewPlus">
        <attr name="normalCardElevation" format="dimension" />
        <attr name="pressedCardElevation" format="dimension" />
        <attr name="cardPressedColor" format="color" />
        <attr name="animationEnabled" format="boolean" />
        <attr name="clickDelay" format="integer" />
    </declare-styleable>


    <declare-styleable name="VibeHeaderView">
        <attr name="centerTitle" format="string" />
        <attr name="showRightBtn" format="boolean" />
        <attr name="rightBtnTitle" format="string" />
        <attr name="rightBtnIcon" format="reference" />
    </declare-styleable>

    <declare-styleable name="HeaderButton">
        <attr name="icon" format="reference" />
        <attr name="title" format="string" />
    </declare-styleable>


</resources>