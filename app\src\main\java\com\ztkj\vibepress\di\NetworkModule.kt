package com.ztkj.vibepress.di

import com.ztkj.vibepress.app.network.GeoService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * @CreateTime : 2023/8/11 10:02
 * <AUTHOR> AppOS
 * @Description :
 */
@InstallIn(SingletonComponent::class)
@Module
object NetworkModule {
    @Singleton
    @Provides
    fun provideGeoService(): GeoService {
        return GeoService.create()
    }
}