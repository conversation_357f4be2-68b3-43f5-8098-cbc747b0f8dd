<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.LoginViewModel" />

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.login.OfflineLoginFragment.ProxyClick" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.fragment.login.OfflineLoginFragment">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/serialNumber"
            style="@style/LoginEdittextStyle"
            android:layout_marginTop="70dp"
            android:text="@={vm.username}"
            android:hint="@string/serialNumber" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/login"
            style="@style/BoardSettingMaterialButtonStyle"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:onClick="@{ () -> click.login()}"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:text="@string/confirm"
            app:cornerRadius="6dp" />

    </LinearLayout>
</layout>