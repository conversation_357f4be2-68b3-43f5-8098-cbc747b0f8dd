<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.MultiSettingViewModel" />

        <variable
            name="config"
            type="com.ztkj.vibepress.data.model.bean.SerialConfigBean" />


        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.setting.MultiSettingFragment.ProxyClick" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.fragment.setting.MultiSettingFragment">

        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/actionBarSize">

            <com.google.android.material.button.MaterialButton
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginEnd="16dp"
                android:backgroundTint="#4D2FF2FF"
                android:drawableStart="@drawable/ic_save"
                android:onClick="@{() -> click.saveConfig()}"
                android:text="@string/save"
                app:cornerRadius="6dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnStartOrStopService"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnStartOrStopService"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginEnd="16dp"
                android:backgroundTint="#4D2FF2FF"
                android:onClick="@{() -> click.startOrStopService()}"
                android:text="服务状态"
                app:cornerRadius="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </com.ztkj.vibepress.app.widget.customview.VibeHeaderView>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!--<TableLayout style="@style/MultiSettingTableLayoutStyle">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/MultiSettingCellTitleStyle"
                        android:text="@string/network_setting"
                        app:drawableStartCompat="@mipmap/ic_network_setting" />

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/dataCentral" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/dataCenterHost"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="@={vm.dataCenterHost}" />
                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/ip_port" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/dataCenterPort"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="@={vm.dataCenterPort}" />


                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/differencePort" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/differenceHost"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="@={vm.differenceHost}" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/ip_port" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/differencePort"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="@={vm.differencePort}" />
                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/thirdPartyPort" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/thirdPartyHost"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="***********" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/ip_port" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/thirdPartyPort"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="60014" />


                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/protocolType" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/protocolType"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="***********" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/ip_port" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/additionalItem"
                            style="@style/MultiSettingCellEditTextStyle"
                            android:text="/taa/w/1" />
                    </TableRow>
                </TableLayout>-->

                <TableLayout style="@style/MultiSettingTableLayoutStyle">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/MultiSettingCellTitleStyle"
                        android:text="@string/gnss_setting"
                        app:drawableStartCompat="@mipmap/ic_setting_gnss" />

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/internalGnssPort" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/internalGnssPath"
                            style="@style/MultiSettingDropDownStyle"
                            android:onClick="@{() -> click.internalGnssPath()}"
                            android:text="@={config.rtkPath}" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/baud" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/internalGnssBaudrate"
                            style="@style/MultiSettingDropDownStyle"
                            android:onClick="@{() -> click.internalGnssBaudrate()}"
                            android:text="@={config.rtkBaudrate}" />


                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/externalGnssPort" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/externalGnssPath"
                            style="@style/MultiSettingDropDownStyle"
                            android:onClick="@{() -> click.externalGnssPath()}"
                            android:text="@={config.externalGnssPath}" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/baud" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/externalGnssBaudrate"
                            style="@style/MultiSettingDropDownStyle"
                            android:onClick="@{() -> click.externalGnssBaudrate()}"
                            android:text="@={config.externalGnssBaudrate}" />
                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <RadioGroup
                            android:id="@+id/radioGroup"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checkedButton="@+id/radio_button_1"
                            android:orientation="horizontal">

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/radio_button_1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:checked="true"
                                android:text="@string/internalGnss"
                                android:textColor="@color/white"
                                android:textSize="18sp" />

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/radio_button_2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="40dp"
                                android:text="@string/externalGnss"
                                android:textColor="@color/white"
                                android:textSize="18sp" />
                        </RadioGroup>
                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <RadioGroup
                            android:id="@+id/diffRadioGroup"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checkedButton="@+id/radio_button_1"
                            android:orientation="horizontal">

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/radioNetworkDifference"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:checked="true"
                                android:text="@string/network_diff"
                                android:textColor="@color/white"
                                android:textSize="18sp" />

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/radioHardDifference"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="40dp"
                                android:text="@string/hardware_diff"
                                android:textColor="@color/white"
                                android:textSize="18sp" />

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/radioAutoSelect"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="40dp"
                                android:text="@string/auto_judgement"
                                android:textColor="@color/white"
                                android:textSize="18sp" />
                        </RadioGroup>
                    </TableRow>

                </TableLayout>

                <TableLayout style="@style/MultiSettingTableLayoutStyle">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/MultiSettingCellTitleStyle"
                        android:text="@string/sensor_setting"
                        app:drawableStartCompat="@mipmap/ic_setting_sensor" />

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/sensor_port" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/sensorPath"
                            style="@style/MultiSettingDropDownStyle"
                            android:onClick="@{() -> click.sensorPath()}"
                            android:text="@={config.counterPath}" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/baud" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/sensorBaudrate"
                            style="@style/MultiSettingDropDownStyle"
                            android:onClick="@{() -> click.sensorBaudrate()}"
                            android:text="@={config.counterBaudrate}" />

                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/counterValue" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/currentCount"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_marginStart="5dp"
                            android:layout_weight="3"
                            android:background="@mipmap/ic_setting_diagnosis"
                            android:gravity="center_vertical|start"
                            android:paddingStart="5dp"
                            android:paddingEnd="0dp"
                            android:text="@={vm.sensorDataValue}"
                            android:textColor="@color/white"
                            android:textSize="18sp" />


                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/power_outage_reset" />

                        <com.skydoves.powerspinner.PowerSpinnerView
                            android:id="@+id/powerOffSpinner"
                            style="@style/CustomSpinnerStyle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:textColorHint="@color/white"
                            app:spinner_arrow_gravity="end"
                            app:spinner_arrow_padding="8dp"
                            app:spinner_divider_show="false"
                            app:spinner_item_array="@array/power_off"
                            app:spinner_popup_animation="normal"
                            app:spinner_popup_background="#212121"
                            app:spinner_popup_elevation="14dp"
                            app:spinner_selected_item_background="@drawable/selected_item_background"
                            tools:ignore="HardcodedText,UnusedAttribute" />

                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/tension_threshold" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:text="@={vm.tensionThreshold}"
                            android:inputType="numberDecimal"
                            style="@style/MultiSettingCellEditTextStyle" />

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/updateFrequency" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:text="@={vm.updateFrequency}"
                            android:inputType="numberDecimal"
                            style="@style/MultiSettingCellEditTextStyle" />
                    </TableRow>

                    <TableRow style="@style/MultiSettingTableRowStyle">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/MultiSettingCellTextViewStyle"
                            android:text="@string/counterReversing" />

                        <com.skydoves.powerspinner.PowerSpinnerView
                            android:id="@+id/spinnerCounterReversing"
                            style="@style/CustomSpinnerStyle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:textColorHint="@color/white"
                            app:spinner_arrow_gravity="end"
                            app:spinner_arrow_padding="8dp"
                            app:spinner_divider_show="false"
                            app:spinner_item_array="@array/power_off"
                            app:spinner_popup_animation="normal"
                            app:spinner_popup_background="#212121"
                            app:spinner_popup_elevation="14dp"
                            app:spinner_selected_item_background="@drawable/selected_item_background"
                            tools:ignore="HardcodedText,UnusedAttribute" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="5"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <com.google.android.material.checkbox.MaterialCheckBox
                                android:id="@+id/checkboxSettingMode"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:gravity="center"
                                android:buttonTint="@color/white"
                                android:text="@string/settingMode"
                                android:textColor="@color/white"
                                android:textSize="18sp" />
                        </LinearLayout>

                    </TableRow>

                    <TableRow
                        android:layout_marginTop="10dp"
                        android:gravity="center_horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/readSensorSetting"
                                style="@style/BoardSettingMaterialButtonStyle"
                                android:onClick="@{ () -> click.readSensorSetting()}"
                                android:text="@string/readSetting"
                                app:cornerRadius="6dp" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/saveSensorSetting"
                                style="@style/BoardSettingMaterialButtonStyle"
                                android:onClick="@{ () -> click.saveSensorSetting()}"
                                android:text="@string/saveSetting"
                                app:cornerRadius="6dp" />
                        </LinearLayout>
                    </TableRow>
                </TableLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/shape_bordered_textview2">

                    <RadioGroup
                        android:id="@+id/radioGroupSensor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="20dp"
                        android:checkedButton="@+id/radio_button_1"
                        android:orientation="vertical"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/radio_button_3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="@string/internalGnss"
                            android:textColor="@color/white"
                            android:textSize="18sp" />

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/radio_button_4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="传感器"
                            android:textColor="@color/white"
                            android:textSize="18sp" />
                    </RadioGroup>

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etCommand"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="20dp"
                        android:background="@drawable/item_setting_border2"
                        android:hint="@string/board_edittext_tips"
                        android:paddingStart="10dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        android:textColorHint="@color/white"
                        app:layout_constraintBottom_toBottomOf="@id/radioGroupSensor"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/radioGroupSensor"
                        app:layout_constraintTop_toTopOf="@id/radioGroupSensor" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvData"
                        android:layout_width="0dp"
                        android:layout_height="200dp"
                        android:layout_marginTop="10dp"
                        android:background="@drawable/item_setting_border2"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="@id/etCommand"
                        app:layout_constraintStart_toStartOf="@+id/radioGroupSensor"
                        app:layout_constraintTop_toBottomOf="@id/etCommand"
                        tools:listitem="@layout/item_simple_recyclerview_text" />


                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</layout>