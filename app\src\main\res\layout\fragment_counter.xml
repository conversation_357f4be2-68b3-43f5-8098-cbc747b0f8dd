<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.CounterStateViewModel" />

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.home.CounterFragment.ProxyClick" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/windowBackground"
        android:padding="24dp"
        tools:context=".ui.fragment.home.CounterFragment">

        <!-- 模式切换卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardModeSwitch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="计时器模式"
                    android:textColor="@color/colorBlack333"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="start|center_vertical" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnToggleMode"
                    style="@style/Widget.Material3.Button.TonalButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="@{() -> click.toggleMode()}"
                    android:text="@{vm.isCountUpMode ? `切换到倒数模式` : `切换到普通模式`}"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:textSize="14sp"
                    app:cornerRadius="12dp"
                    tools:text="切换到倒数模式" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 倒计时设置卡片 (仅在倒数模式显示) -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardCountdownSetting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="@{vm.isCountUpMode ? android.view.View.GONE : android.view.View.VISIBLE}"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardModeSwitch">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical"
                android:minHeight="60dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="倒计时时长："
                    android:textColor="@color/colorBlack333"
                    android:textSize="16sp" />

                <!-- 分钟输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="4dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:text="@={vm.countdownMinutes}"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:maxLength="2"
                        android:minHeight="48dp"
                        android:afterTextChanged="@{(text) -> vm.onMinutesChanged(text)}" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="分"
                    android:textColor="@color/colorBlack333"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <!-- 秒数输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:text="@={vm.countdownSeconds}"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:maxLength="2"
                        android:minHeight="48dp"
                        android:afterTextChanged="@{(text) -> vm.onSecondsChanged(text)}" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="秒"
                    android:textColor="@color/colorBlack333"
                    android:textSize="16sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 时间显示卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardTimeDisplay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardCountdownSetting">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="40dp"
                android:gravity="center"
                android:minHeight="140dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="@{vm.isCountUpMode ? `正数计时` : `倒数计时`}"
                    android:textColor="@color/colorBlack666"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvTimeDisplay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{vm.formatTime(vm.currentTime)}"
                    android:textColor="@color/colorPrimary"
                    android:textSize="52sp"
                    android:textStyle="bold"
                    android:fontFamily="monospace"
                    android:letterSpacing="0.1"
                    tools:text="00:00" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 控制按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnStartStop"
            android:layout_width="220dp"
            android:layout_height="64dp"
            android:layout_marginTop="16dp"
            android:onClick="@{() -> click.startStopTimer()}"
            android:text="@{vm.isRunning ? `停止` : `开始`}"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="16dp"
            app:iconGravity="textStart"
            app:iconSize="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardTimeDisplay"
            app:layout_constraintVertical_bias="0.0"
            tools:text="开始" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>