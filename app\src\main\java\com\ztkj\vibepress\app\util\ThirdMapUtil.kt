package com.ztkj.vibepress.app.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.widget.Toast
import com.blankj.utilcode.util.ToastUtils
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.ext.toast

/**
 * @CreateTime : 2022/11/4 11:29
 * <AUTHOR> AppOS
 * @Description : 第三方地图工具类 高德地图，百度地图
 */
object ThirdMapUtil {
    const val PN_GAODE_MAP = "com.autonavi.minimap" // 高德地图包名

    const val PN_BAIDU_MAP = "com.baidu.BaiduMap" // 百度地图包名

    const val DOWNLOAD_GAODE_MAP = "http://www.autonavi.com/" // 高德地图下载地址

    const val DOWNLOAD_BAIDU_MAP = "http://map.baidu.com/zt/client/index/" // 百度地图下载地址


    /**
     * 路线规划
     *
     * @param slat 起点纬度
     * @param slon 起点经度
     * @param dlat 终点纬度
     * @param dlon 终点经度
     */
    fun route(context: Context, slat: String?,slon: String?,dlat: String,dlon: String,dname: String?){
        if (isInstallApp(context,PN_GAODE_MAP)) {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.setPackage("com.autonavi.minimap")
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            var uri =
                "androidamap://route?" + "sourceApplication=" + context.getString(R.string.app_name)
            //如果设置了起点
            if (!TextUtils.isEmpty(slat) && !TextUtils.isEmpty(slon)) {
                uri += "&slat=$slat&slon=$slon"
            }
            uri += "&dlat=$dlat&dlon=$dlon&dname=$dname&dev=0&t=0"
            intent.data = Uri.parse(uri)
            context.startActivity(intent)
        }else {
            "未检测到高德地图App".toast()
        }
    }

    /**
     * 打开百度地图导航功能(默认坐标点是高德地图，需要转换)
     * @param context
     * @param slat 起点纬度
     * @param slon 起点经度
     * @param sname 起点名称 可不填（0,0，null）
     * @param dlat 终点纬度
     * @param dlon 终点经度
     * @param dname 终点名称 必填
     */
    fun openBaiDuNavi(context: Context, slon: Double,slat: Double,sname: String,dlon: Double, dlat: Double,dname: String?
    ) {
        if (isInstallApp( context, PN_BAIDU_MAP)) {
            val uriString: String?
            val builder = StringBuilder("baidumap://map/direction?mode=driving&")
            if (slat != 0.0) {
                //起点坐标转换
                builder.append("origin=latlng:")
                    .append(slat)
                    .append(",")
                    .append(slon)
                    .append("|name:")
                    .append(sname)
            }
            builder.append("&destination=latlng:")
                .append(dlat)
                .append(",")
                .append(dlon)
                .append("|name:")
                .append(dname)
                .append("&coord_type=")
                .append("gcj02")
            uriString = builder.toString()
            val intent = Intent(Intent.ACTION_VIEW)
            intent.setPackage(PN_BAIDU_MAP)
            intent.data = Uri.parse(uriString)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        } else {
            "未检测到百度地图app".toast()
        }
    }

    private fun isInstallApp(context: Context, packageName: String) : Boolean {
        val packageInfo = try {
            context.packageManager.getPackageInfo(packageName,0)
        }catch (e: Exception) {
            e.printStackTrace()
            null
        }
        return packageInfo != null
    }

}