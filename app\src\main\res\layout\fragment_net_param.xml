<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="config"
            type="com.ztkj.vibepress.data.model.bean.NetConfigBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.NetParamFragment">


        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:centerTitle="@string/network_parameters_setting"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:showRightBtn="true" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/centerVerticalLine"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintGuide_percent="0.5"
            android:orientation="vertical" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/diffTitle"
            style="@style/NetParamSettingTitleTextView"
            android:layout_marginStart="48dp"
            android:layout_marginTop="16dp"
            android:text="@string/differentiated_services"
            app:drawableStartCompat="@drawable/ic_differentiated_services"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etDiffHost"
            android:text="@={config.diffHost}"
            style="@style/NetParamSettingIPEditText"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            app:layout_constraintEnd_toStartOf="@+id/centerVerticalLine"
            app:layout_constraintTop_toBottomOf="@+id/diffTitle" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/NetParamSettingDescTextView"
            android:text="@string/server_ip_port"
            app:layout_constraintBaseline_toBaselineOf="@+id/etDiffHost"
            app:layout_constraintEnd_toStartOf="@+id/etDiffHost" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/NetParamSettingAlphaEditText"
            android:id="@+id/etDiffIP"
            android:text="@={config.diffIP}"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            app:layout_constraintTop_toBottomOf="@id/etDiffHost"
            app:layout_constraintEnd_toEndOf="@id/centerVerticalLine" />

        <androidx.appcompat.widget.AppCompatTextView
            android:text="@string/ip_port"
            style="@style/NetParamSettingDescTextView"
            app:layout_constraintEnd_toStartOf="@id/etDiffIP"
            app:layout_constraintBaseline_toBaselineOf="@id/etDiffIP" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/dataCenterTitle"
            style="@style/NetParamSettingTitleTextView"
            android:layout_marginStart="5dp"
            app:layout_constraintBaseline_toBaselineOf="@+id/diffTitle"
            app:layout_constraintStart_toStartOf="@id/centerVerticalLine"
            android:text="@string/data_center_services"
            app:drawableStartCompat="@drawable/ic_datacenter_service" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etDataCenterHost"
            style="@style/NetParamSettingIPEditText"
            android:text="@={config.dataCenterHost}"
            android:layout_marginEnd="25dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBaseline_toBaselineOf="@id/etDiffHost" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/NetParamSettingDescTextView"
            android:text="@string/server_ip_port"
            app:layout_constraintBaseline_toBaselineOf="@+id/etDiffHost"
            app:layout_constraintEnd_toStartOf="@+id/etDataCenterHost" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/NetParamSettingAlphaEditText"
            android:id="@+id/etDataCenterIP"
            android:text="@={config.dataCenterIP}"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            app:layout_constraintTop_toBottomOf="@id/etDataCenterHost"
            app:layout_constraintEnd_toEndOf="@id/etDataCenterHost" />

        <androidx.appcompat.widget.AppCompatTextView
            android:text="@string/ip_port"
            style="@style/NetParamSettingDescTextView"
            app:layout_constraintEnd_toStartOf="@id/etDataCenterIP"
            app:layout_constraintBaseline_toBaselineOf="@id/etDataCenterIP" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/dataDockingTitle"
            style="@style/NetParamSettingTitleTextView"
            android:layout_marginTop="16dp"
            android:text="@string/data_docking"
            app:drawableStartCompat="@drawable/ic_data_docking"
            app:layout_constraintStart_toStartOf="@id/diffTitle"
            app:layout_constraintTop_toBottomOf="@+id/etDiffIP" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/NetParamBorderTextView"
            android:text="@={config.dockingPlatform}"
            android:id="@+id/etDataDockingServer"
            app:layout_constraintEnd_toEndOf="@id/centerVerticalLine"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            app:layout_constraintTop_toBottomOf="@id/dataDockingTitle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDataDockingServer"
            style="@style/NetParamSettingDescTextView"
            android:text="@string/server_port_platform"
            app:layout_constraintBaseline_toBaselineOf="@+id/etDataDockingServer"
            app:layout_constraintEnd_toStartOf="@+id/etDataDockingServer" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/NetParamSettingAlphaEditText"
            android:id="@+id/etDataDockingPort"
            android:text="@={config.dockingHost}"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            app:layout_constraintTop_toBottomOf="@id/etDataDockingServer"
            app:layout_constraintEnd_toEndOf="@id/centerVerticalLine" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/NetParamSettingDescTextView"
            android:text="@string/server_ip_port"
            app:layout_constraintBaseline_toBaselineOf="@+id/etDataDockingPort"
            app:layout_constraintEnd_toStartOf="@+id/etDataDockingPort" />


        <androidx.appcompat.widget.AppCompatEditText
            style="@style/NetParamSettingAlphaEditText"
            android:id="@+id/etDataDockingIP"
            android:text="@={config.dockingIP}"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            app:layout_constraintTop_toBottomOf="@id/etDataDockingPort"
            app:layout_constraintEnd_toEndOf="@id/centerVerticalLine" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/NetParamSettingDescTextView"
            android:text="@string/ip_port"
            app:layout_constraintBaseline_toBaselineOf="@+id/etDataDockingIP"
            app:layout_constraintEnd_toStartOf="@+id/etDataDockingIP" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>