package com.ztkj.vibepress.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.InputType
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import androidx.core.widget.doAfterTextChanged
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseMultiItemAdapter
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.ext.getString
import com.ztkj.vibepress.app.util.VibeConfig
import com.ztkj.vibepress.data.model.bean.DeviceSettingEntity
import com.ztkj.vibepress.data.model.bean.FieldType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType
import com.ztkj.vibepress.databinding.ItemSettingDropdownListBinding
import com.ztkj.vibepress.databinding.ItemSettingEdittextBinding

/**
 * @CreateTime : 2023/6/2 9:33
 * <AUTHOR> AppOS
 * @Description :
 */
class DeviceSettingAdapter(
    data: MutableList<DeviceSettingEntity>, var dataMap: MutableMap<String, String> = mutableMapOf()
) : BaseMultiItemAdapter<DeviceSettingEntity>(data) {

    class ItemTextFieldVH(val binding: ItemSettingEdittextBinding) :
        RecyclerView.ViewHolder(binding.root)

    class ItemDropdownListVH(val binding: ItemSettingDropdownListBinding) :
        RecyclerView.ViewHolder(binding.root)

    init {
        addItemType(FieldType.TEXT_FIELD.ordinal,
            object : OnMultiItemAdapterListener<DeviceSettingEntity, ItemTextFieldVH> {
                @SuppressLint("SetTextI18n")
                override fun onBind(
                    holder: ItemTextFieldVH, position: Int, item: DeviceSettingEntity?
                ) {
                    if (item == null) return
                    holder.binding.apply {
                        title.text = item.title + ":"
                        if (item.inputType != InputType.TYPE_NUMBER_FLAG_DECIMAL) {
                            edittext.inputType = item.inputType
                        }
                        edittext.maxLines = 1
                        edittext.hint = getString(R.string.please_enter) + item.title
                        edittext.setText(getContentProperty(item.title))
                        edittext.doAfterTextChanged {
                            setContentProperty(item.title, it.toString())
                        }
                    }
                    holder.binding.edittext.setOnEditorActionListener { _, actionId, _ ->
                        if (actionId == EditorInfo.IME_ACTION_NEXT) {
                            if (position < data.size - 1) {
                                // 获取下一个 Item 的 EditText 控件，并请求焦点
                                val nextItemEditText =
                                    (holder.itemView.parent as RecyclerView).findViewHolderForAdapterPosition(
                                        position + 1
                                    )?.itemView?.findViewById<EditText>(R.id.edittext)
                                nextItemEditText?.requestFocus()
                            }
                            true
                        } else {
                            false
                        }
                    }
                }

                override fun onCreate(
                    context: Context, parent: ViewGroup, viewType: Int
                ): ItemTextFieldVH {
                    val binding = ItemSettingEdittextBinding.inflate(
                        LayoutInflater.from(context), parent, false
                    )
                    return ItemTextFieldVH(binding)
                }

            }).addItemType(FieldType.DROPDOWN_LIST.ordinal,
            object : OnMultiItemAdapterListener<DeviceSettingEntity, ItemDropdownListVH> {
                @SuppressLint("SetTextI18n")
                override fun onBind(
                    holder: ItemDropdownListVH, position: Int, item: DeviceSettingEntity?
                ) {
                    if (item == null) return
                    holder.binding.run {
                        title.text = item.title + ":"
                    }
                    holder.binding.spinner.run {
                        val spinnerValue = getSpinnerContentProperty(item.title)
                        text = spinnerValue
                        hint = "请选择${item.title}"
                        setItems(item.spinnerList!!)
                        setOnSpinnerOutsideTouchListener { _, _ ->
                            dismiss()
                        }
                        doAfterTextChanged {
                            setDropListContentProperty(item.title, it.toString())
                        }
                    }
                }

                override fun onCreate(
                    context: Context, parent: ViewGroup, viewType: Int
                ): ItemDropdownListVH {
                    val binding = ItemSettingDropdownListBinding.inflate(
                        LayoutInflater.from(context), parent, false
                    )
                    return ItemDropdownListVH(binding)
                }

            }).onItemViewType { position, list -> // 根据数据，返回对应的 ItemViewType
            when (list[position].fieldType) {
                FieldType.TEXT_FIELD -> FieldType.TEXT_FIELD.ordinal
                FieldType.DROPDOWN_LIST -> FieldType.DROPDOWN_LIST.ordinal
            }
        }
    }


    private fun getContentProperty(descName: String): String? {
        return dataMap[getFieldNameByDescription(descName)]
    }

    private fun setContentProperty(descName: String, fieldValue: String) {
        dataMap[getFieldNameByDescription(descName)] = fieldValue
    }

    private fun getSpinnerContentProperty(descName: String): String? {
        //先获取dataMap 字段的真实值
        val realValue = dataMap[getFieldNameByDescription(descName)]
        if (descName == getString(R.string.installsLoc)) {
            return VibeConfig.getInstallLocDescNameByKey(realValue)
        } else if (descName == getString(R.string.installsWay)) {
            return VibeConfig.getInstallWayDescNameByKey(realValue)
        }
        return null
    }

    private fun setDropListContentProperty(descName: String, fieldValue: String) {
//        val fieldName = dataMap[getFieldNameByDescription(descName)]
        if (descName == getString(R.string.installsLoc)) {
            dataMap[getFieldNameByDescription(descName)] =
                VibeConfig.getInstallLocRealValue(fieldValue)
        } else if (descName == getString(R.string.installsWay)) {
            dataMap[getFieldNameByDescription(descName)] =
                VibeConfig.getInstallWayRealValue(fieldValue)
        }
    }

    private fun getFieldNameByDescription(descName: String): String {
        return when (descName) {
            getString(R.string.device_name) -> "name"
            getString(R.string.serialNumber) -> "serialNumber"
            getString(R.string.horizontalDistance) -> "ftxDiatance"
            getString(R.string.verticalDistance) -> "zftxVerticalDiatance"
            getString(R.string.aerialsHammerAngle) -> "aerialsHammerAngle"
            getString(R.string.antennaHeight) -> "antennaHeight"
            getString(R.string.installsWay) -> "installsWay"
            getString(R.string.installsLoc) -> "installsLoc"
            getString(R.string.cableCount) -> "cableCount"
            getString(R.string.referencePoint) -> "referencePoint"
            getString(R.string.magnetNum) -> "magnetNum"
            getString(R.string.windingEnginePerimeter) -> "windingEnginePerimeter"
            getString(R.string.magnetDistance) -> "magnetDistance"
            getString(R.string.hitThreshold) -> "hitThreshold"
            getString(R.string.clusterThreshold) -> "clusterThreshold"
            getString(R.string.tensionThreshold) -> "tensionThreshold"
            else -> ""
        }

    }


}

