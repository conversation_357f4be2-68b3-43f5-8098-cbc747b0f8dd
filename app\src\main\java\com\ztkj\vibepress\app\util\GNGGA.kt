package com.ztkj.vibepress.app.util

import com.ztkj.sensorlib.utils.NmeaAnalysisUtil

object GNGGA {
    fun generateGNGGA(gpggaData: String): String {
        val gngga = gpggaData.replace("\$GNGGA", "GPGGA")
        val findDataBeforeStar = findDataBeforeStar(gngga)
        return NmeaAnalysisUtil.packStringToNmeaFormat(findDataBeforeStar)
    }

    fun findDataBeforeStar(data: String): String {
        // 找到星号的位置
        val starIndex = data.indexOf("*")

        // 如果找到星号，则返回星号之前的数据
        if (starIndex != -1) {
            return data.substring(0, starIndex)
        }

        // 如果没有找到星号，则返回整个数据
        else {
            return data
        }
    }
}