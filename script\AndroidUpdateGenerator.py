import json
import os
from androguard.core.bytecodes import apk
import hashlib
from datetime import datetime
import zipfile
import sys


# 获取 APK 文件的相对路径
def find_apk_file():
    apk_file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "../app/build/outputs/apk/release"))
    for filename in os.listdir(apk_file_path):
        if filename.endswith(".apk"):
            return os.path.join(apk_file_path, filename)
    return None


# 获取 APK 文件信息
def get_apk_info(apk_file_path):
    a = apk.APK(apk_file_path)
    version_code = a.get_androidversion_code()
    version_name = a.get_androidversion_name()
    apk_size_bytes = os.path.getsize(apk_file_path)

    apk_size_kb = int(apk_size_bytes / 1024)

    with open(apk_file_path, "rb") as f:
        data = f.read()
    apk_md5 = hashlib.md5(data).hexdigest()

    return version_code, version_name, apk_size_kb, apk_md5


# 获取当前系统时间并格式化为字符串
def get_current_time():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


# 获取下载 URL
def get_download_url(apk_file_name):
    base_url = "http://218.77.59.2:6212/android/dcm/app/"
    return base_url + apk_file_name


def update_json_data(param1, param2=1):
    apk_file_path = find_apk_file()

    if apk_file_path:
        version_code, version_name, apk_size, apk_md5 = get_apk_info(apk_file_path)

        # 如果 param1 (modifyContent) 为空，则设置默认内容
        if not param1:
            param1 = f"V{version_name}更新信息"
        data = {
            "Code": 0,
            "Msg": "",
            "UpdateStatus": param2,
            "ModifyContent": param1,
            "VersionCode": int(version_code),
            "VersionName": version_name,
            "ApkMd5": apk_md5,
            "ApkSize": apk_size,
            "UploadTime": get_current_time(),
            "DownloadUrl": get_download_url(os.path.basename(apk_file_path))
        }

        with open("update.json", "w", encoding="utf-8") as json_file:
            json.dump(data, json_file, ensure_ascii=False, indent=4)
    else:
        print("未找到 APK 文件在当前目录中.")


# 打包 APK 和 JSON 到 ZIP 文件
def create_zip_file():
    apk_file_path = find_apk_file()

    if apk_file_path:
        with zipfile.ZipFile("release.zip", "w", zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(apk_file_path, os.path.basename(apk_file_path))
            zipf.write("update.json", "update.json")

        # 删除 update.json 文件
        os.remove("update.json")


if __name__ == "__main__":
    modifyContent = sys.argv[1] if len(sys.argv) > 1 else ""
    updateStatus = sys.argv[2] if len(sys.argv) > 2 else "1"

    if updateStatus not in ("1", "2", ""):
        print("参数异常，请仔细阅读README")
    else:
        updateStatus = int(updateStatus)
        update_json_data(modifyContent, updateStatus)
        # create_zip_file()
        print("研发生产部--超级工程师--郭锐 已经为您处理成功")
