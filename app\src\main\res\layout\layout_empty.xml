<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_empty"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/loading_emptyimg"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@drawable/load_empty" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:text="暂时没有数据"
        android:textColor="#333333"
        android:textSize="14dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:text="点击我重试"
        android:textColor="#666666"
        android:textSize="14dp" />
</LinearLayout>
