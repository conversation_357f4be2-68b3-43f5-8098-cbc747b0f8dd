package com.ztkj.vibepress.app.network;

import android.content.Context;
import android.util.Log;

import com.luck.picture.lib.entity.LocalMedia;
import com.ztkj.baselib.pictureselector.ImagePickerUtils;

import java.io.File;
import java.util.List;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class ImagePickerUploadCallBack implements ImagePickerUtils.UploadCallBack {
    @Override
    public void upload(Context context, List<LocalMedia> list, ImagePickerUtils.CallBack callBack) {
        Log.e("TAG", "upload: ");
    }


    public static List<MultipartBody.Part> getUploadParts(List<LocalMedia> localMediaList) {
        //创建MultipartBody.Builder对象
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        for (LocalMedia localMedia : localMediaList) {
//            Log.e("123", "path:" + localMedia);
            String replace = localMedia.getCompressPath().trim();
//            Log.e("123", "replace:" + replace);
            File file = new File(replace);
    /*        Log.e("123", "exists:" + file.exists());
            Log.e("123", "canWrite:" + file.canWrite());
            Log.e("123", "canRead:" + file.canRead());
            Log.e("123", "getAbsolutePath:" + file.getAbsolutePath());*/
            //表单类型
            RequestBody body = RequestBody.create(MediaType.parse("multipart/form-data"), file);
            //添加图片数据，body创建的请求体
            //files:参数名称
            builder.addFormDataPart("files", file.getName(), body);
        }
        return builder.build().parts();
    }
}
