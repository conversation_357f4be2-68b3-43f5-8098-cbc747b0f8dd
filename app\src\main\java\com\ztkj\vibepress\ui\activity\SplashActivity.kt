package com.ztkj.vibepress.ui.activity

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.animation.Animation
import android.view.animation.ScaleAnimation
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.vibepress.app.base.BaseActivity
import com.ztkj.vibepress.app.ext.askForPermissions
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.databinding.ActivitySplashBinding

@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity<BaseViewModel, ActivitySplashBinding>() {
    private val scaleAnimation = ScaleAnimation(
        1f,
        1.05f,
        1f,
        1.05f,
        Animation.RELATIVE_TO_SELF,
        0.5f,
        Animation.RELATIVE_TO_SELF,
        0.5f
    )


    override fun initView(savedInstanceState: Bundle?) {
        binding.click = ProxyClick()
        requestPermission()
    }

    private fun initAppSetup() {
        initAnimation()
        startAnimation()
        addAnimationListener()
    }

    private fun initAnimation() {
        val splashDuration = 2 * 1000L
        scaleAnimation.duration = splashDuration
        scaleAnimation.fillAfter = true
        scaleAnimation.repeatMode = ScaleAnimation.RELATIVE_TO_SELF
        scaleAnimation.repeatCount = 2
    }

    private fun addAnimationListener() {
        scaleAnimation.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(p0: Animation?) {

            }

            override fun onAnimationEnd(p0: Animation?) {
                toMain()
            }

            override fun onAnimationRepeat(p0: Animation?) {

            }

        })
    }

    private fun startAnimation() {
        binding.ivSplashPicture.startAnimation(scaleAnimation)
    }

    private fun stopAnimation() {
        scaleAnimation.cancel()
        binding.ivSplashPicture.clearAnimation()
    }

    fun toMain() {
        startActivity(Intent(this@SplashActivity, MainActivity::class.java))
        finish()
        //带点渐变动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }

    override fun onDestroy() {
        super.onDestroy()
        stopAnimation()
    }

    private fun requestPermission() = askForPermissions(
        listOf(
            Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA, Manifest.permission.INTERNET,
            Manifest.permission.READ_PHONE_STATE, Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.ACCESS_WIFI_STATE, Manifest.permission.CHANGE_WIFI_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION
        )
    ) { allGranted, _, deniedList ->
        if (!allGranted) {
            "These permissions are denied: $deniedList".toast()
        } else {
            initAppSetup()
        }
    }


    inner class ProxyClick {

    }
}
