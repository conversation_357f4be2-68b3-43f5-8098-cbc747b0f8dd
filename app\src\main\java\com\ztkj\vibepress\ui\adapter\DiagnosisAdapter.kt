package com.ztkj.vibepress.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseDifferAdapter
import com.ztkj.vibepress.R
import com.ztkj.vibepress.data.model.bean.DiagnosisItem
import com.ztkj.vibepress.databinding.ItemSettingDiagnosisBinding
import javax.inject.Inject

/**
 * @CreateTime : 2023/6/9 14:15
 * <AUTHOR> AppOS
 * @Description :
 */
class DiagnosisAdapter @Inject constructor(diffEntityCallback: DiffEntityCallback) :
    BaseDifferAdapter<DiagnosisItem, DiagnosisAdapter.VH>(diffEntityCallback) {

    class VH(
        parent: ViewGroup,
        val binding: ItemSettingDiagnosisBinding = ItemSettingDiagnosisBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: VH, position: Int, item: DiagnosisItem?) {
        if (item == null) return
        holder.binding.run {
            title.text = context.getString(R.string.item_value_format, item.title)
            content.text = item.value ?: "暂未获取到数据"
        }
    }

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(parent)
    }

    class DiffEntityCallback @Inject constructor() : DiffUtil.ItemCallback<DiagnosisItem>() {
        override fun areItemsTheSame(oldItem: DiagnosisItem, newItem: DiagnosisItem): Boolean {
            return oldItem.title === newItem.title
        }

        override fun areContentsTheSame(oldItem: DiagnosisItem, newItem: DiagnosisItem): Boolean {
            return oldItem.value == newItem.value
        }

        override fun getChangePayload(oldItem: DiagnosisItem, newItem: DiagnosisItem): Any? {
            return null
        }

    }


}