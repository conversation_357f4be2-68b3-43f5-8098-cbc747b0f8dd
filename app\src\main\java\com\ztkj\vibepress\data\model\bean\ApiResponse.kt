package com.ztkj.vibepress.data.model.bean

import com.ztkj.baselib.network.BaseResponse

/**
 * @CreateTime : 2022/10/17 9:49
 * <AUTHOR> AppOS
 * @Description : 服务器返回数据的基类
 * 1.继承 BaseResponse
 * 2.重写isSuccess 方法，编写你的业务需求，根据自己的条件判断数据是否请求成功
 * 3.重写 getResponseCode、getResponseData、getResponseMsg方法，传入你的 code data msg
 */
data class ApiResponse<T>(val errorCode: Int, val errorMsg: String, val data: T) :
    BaseResponse<T>() {

    // 错误码为 0 就代表请求成功，
    override fun isSuccess() = errorCode == 0

    override fun getResponseCode() = errorCode

    override fun getResponseData() = data

    override fun getResponseMsg() = errorMsg

}
