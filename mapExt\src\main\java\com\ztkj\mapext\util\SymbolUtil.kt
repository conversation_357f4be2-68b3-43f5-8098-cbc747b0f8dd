package com.ztkj.mapext.util

import com.esri.arcgisruntime.symbology.CompositeSymbol
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol
import com.esri.arcgisruntime.symbology.TextSymbol

/**
 * @CreateTime : 2023/7/25 14:26
 * <AUTHOR> AppOS
 * @Description :
 */
object SymbolUtil {

    fun buildTampingPointSymbol(text: String): CompositeSymbol {
        // Create a CompositeSymbol to hold the combination of symbols
        val compositeSymbol = CompositeSymbol()

        // Create a green circle symbol and add it to the CompositeSymbol
        val circleSymbol = SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, 0x458D30, 20f)
        compositeSymbol.symbols.add(circleSymbol)

        // Create a TextSymbol to show the text and add it to the CompositeSymbol
        val textSymbol = TextSymbol(
            10f, text, 0xFFFFFF,
            TextSymbol.HorizontalAlignment.CENTER, TextSymbol.VerticalAlignment.MIDDLE
        )
        compositeSymbol.symbols.add(textSymbol)

        return compositeSymbol
    }
}