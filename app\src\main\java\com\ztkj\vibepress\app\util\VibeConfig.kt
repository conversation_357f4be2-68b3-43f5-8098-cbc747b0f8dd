package com.ztkj.vibepress.app.util

import com.ztkj.baselib.base.appContext
import com.ztkj.vibepress.R
import com.ztkj.vibepress.data.model.bean.NetConfigBean
import com.ztkj.vibepress.data.model.bean.SerialConfigBean
import com.ztkj.vibepress.data.model.kEnum.TampWorkType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType.FULL
import com.ztkj.vibepress.data.model.kEnum.TampWorkType.POINT

/**
 * @CreateTime : 2023/6/13 17:29
 * <AUTHOR> AppOS
 * @Description :
 */
object VibeConfig {

    fun getDefaultNetworkConfigBean(): NetConfigBean {
        return NetConfigBean(
            "***********",
            "60004",
            "***********",
            "6201",
            "北京四维",
            "***********",
            "8888"
        )
    }

    fun getDefaultSerialPortConfigBean(): SerialConfigBean = SerialConfigBean(
        "/dev/ttyWK2",
        "115200",
        "dev/ttyWK3",
        "115200",
        "/dev/ttyHSL2",
        "19200"
    )

    /**
     * 作业类型配置
     */
    fun getWorkTypeConfig(): Map<String, String> {
        val workTypes = appContext.resources.getStringArray(R.array.workType)
        return workTypes.mapIndexed { index, value -> index.toString() to value }.toMap()
    }

    fun getWorkTypeDescriptionNameByKey(key: String?): String? {
        return getWorkTypeConfig()[key]
    }

    fun getWorkTypeRealValue(workType: TampWorkType): Int {
        return when (workType) {
            POINT -> 0
            FULL -> 1
        }
    }

    private fun getInstallLocationConfig(): Map<String, String> {
        val workTypes = appContext.resources.getStringArray(R.array.install_location)
        return workTypes.mapIndexed { index, value -> index.toString() to value }.toMap()
    }

    fun getInstallLocDescNameByKey(key: String?): String? {
        return getInstallLocationConfig()[key]
    }

    fun getInstallLocRealValue(mValue: String): String {
        return getInstallLocationConfig().entries.find { it.value == mValue }?.key ?: ""
    }

    fun getInstallWayConfig(): Map<String, String> {
        val workTypes = appContext.resources.getStringArray(R.array.install_way)
        return workTypes.mapIndexed { index, value -> index.toString() to value }.toMap()
    }

    fun getInstallWayDescNameByKey(key: String?): String? {
        return getInstallWayConfig()[key]
    }

    fun getInstallWayRealValue(mValue: String): String {
        return getInstallWayConfig().entries.find { it.value == mValue }?.key ?: ""
    }

    fun getRequireDistanceDescNameByKey(key: String?, workType: TampWorkType): String? {
        return getRequireDistanceConfig(workType)[key]
    }


    fun getRequireDistanceRealValue(mValue: String, workType: TampWorkType): String {
        return findDistanceKeyByValue(mValue, workType)
    }

    private fun findDistanceKeyByValue(
        mValue: String,
        workType: TampWorkType = POINT
    ): String {
        return getRequireDistanceConfig(workType).entries.find { it.value == mValue }?.key ?: ""
    }


    /**
     * 获取中心距/搭接 配置
     */
    private fun getRequireDistanceConfig(workType: TampWorkType = POINT): Map<String, String> {
        return when (workType) {
            POINT -> {
                mapOf(
                    "2.5" to "两米五",
                    "3" to "三米",
                    "3.5" to "三米五",
                    "4" to "四米",
                    "4.5" to "四米五"
                )
            }

            FULL -> {
                mapOf(
                    "0.5" to "二分之一",
                    "0.33" to "三分之一",
                    "0.25" to "四分之一",
                    "0.2" to "五分之一"
                )
            }
        }
    }
}