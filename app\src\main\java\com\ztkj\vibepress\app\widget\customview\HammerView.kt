package com.ztkj.vibepress.app.widget.customview

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewTreeObserver
import android.view.animation.LinearInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import com.ztkj.vibepress.databinding.LayoutHammerViewBinding

/**
 * @CreateTime : 2023/6/28 11:01
 * <AUTHOR> AppOS
 * @Description :
 */
class HammerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val minValue = 0f // 数据的最小值
        private const val maxValue = 25f // 数据的最大值
        private const val animationMinValue = 0f // 动画位置的最小值
        private const val animationMaxValue = -268f // 动画位置的最大值
        private const val TAG = "HammerView"
    }

    private val binding: LayoutHammerViewBinding

    // Define the ValueAnimator as a member variable
    private var valueAnimator: ValueAnimator? = null

    init {
        binding = LayoutHammerViewBinding.inflate(LayoutInflater.from(context), this, true)
        attrs?.let { applyAttributes(it) }
        initAnimation()
        /*        binding.xu.viewTreeObserver
                    .addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            // 这里可以获取ImageView的高度
                            val height: Int = binding.xu.height
                            Log.e("TAG", "onGlobalLayout: ${height}")
                            // 在获取到高度之后，您可能希望移除监听器，以免多次触发
                            binding.xu.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        }
                    })*/
    }

    private fun initAnimation() {

        if (valueAnimator == null) {
            valueAnimator = ValueAnimator.ofFloat(
                animationMinValue,
                animationMaxValue
            )
            valueAnimator?.run {
                duration = 1000L
                interpolator = LinearInterpolator()
                addUpdateListener { animator ->
                    val animatedValue = animator.animatedValue as Float
                    binding.chui.translationY = animatedValue
                }
            }
        }
    }


    private fun applyAttributes(attrs: AttributeSet) {
        /*        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.HeaderButton)
                val title = typedArray.getString(R.styleable.HeaderButton_title)
                title?.let { binding.title.text = it }
                val iconRes = typedArray.getResourceId(R.styleable.HeaderButton_icon, 0)
                binding.icon.setImageResource(iconRes)
                typedArray.recycle()*/
    }

    fun setAttribute(text: String, iconRes: Int) {
        /*        binding.title.text = text
                binding.icon.setImageResource(iconRes)*/
    }

    private fun updateAnimationHeight(data: Float): Float {
        // Calculate the new animation end value based on the newValue and animationMaxValue
        if (data >= maxValue) {
            return animationMaxValue
        } else if (data <= minValue) {
            return 0f
        }
        return animationMaxValue * (data / maxValue)
    }

    /**
     * 夯锤高度
     */
    fun updateHeight(value: Float) {
        val updateAnimationHeight = updateAnimationHeight(value)
        // Set the new end value to the ValueAnimator
        valueAnimator?.setFloatValues(binding.chui.translationY, updateAnimationHeight)
        // Start the animation from the current position
        valueAnimator?.start()
    }

    /**
     * 标高
     */
    fun updateElevationBenchmark(height: Float) {
        val updateAnimationHeight = updateAnimationHeight(height)
        binding.elevationView.translationY = updateAnimationHeight
        binding.elevationView.setElevationText("标高: ${height}m")
    }


}