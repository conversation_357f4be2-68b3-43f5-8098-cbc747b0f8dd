package com.ztkj.vibepress.ui.fragment.setting

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.ztkj.baselib.ext.util.setOnclickNoRepeat
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.delayedLoading
import com.ztkj.vibepress.app.ext.hideSoftKeyboard
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.CalculateUtil
import com.ztkj.vibepress.data.Constants
import com.ztkj.vibepress.data.Constants.TAMP.Companion.MIN_TENSION_VALUE
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.databinding.FragmentCalibrationBinding
import com.ztkj.vibepress.service.BackgroundService
import com.ztkj.vibepress.viewmodel.state.CalibrationViewModel

/**
 * 校准
 */
class CalibrationFragment : BaseFragment<CalibrationViewModel, FragmentCalibrationBinding>() {

    private var boundService: BackgroundService? = null


    override fun initView(savedInstanceState: Bundle?) {
        binding.vm = mViewModel
        binding.click = ProxyClick()
        initToolbar()
        initParams()
    }

    private fun initParams() {
//        binding.tensionValue.text = CacheUtil.getTensionValue().toString()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        bindService()
        binding.hammerHeightRatio.setText(CacheUtil.getHammerHeightRatio().toString())
        /*        delayedLoading(1000L) {
                    clearSensorCount()
                }*/
    }

    private fun initToolbar() {
        binding.toolBar.run {
            initNormalClose(this@CalibrationFragment)
            setOnclickNoRepeat(this) {
                saveLocalData()
            }
        }
    }

    private fun saveLocalData() {
        if (mViewModel.liftingHeightRatio == 0f) {
            "请先设置好锤高系数".toast()
            return
        }
        hideSoftKeyboard(requireActivity())
        CacheUtil.setHammerHeightRatio(mViewModel.liftingHeightRatio)
        "锤高系数${mViewModel.liftingHeightRatio}保存成功".toast()
        binding.hammerHeightRatio.setText(CacheUtil.getHammerHeightRatio().toString())
    }

    private fun bindService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().bindService(intent, mServiceConnection, 0)
    }

    private fun unBindService() {
        boundService?.removeOnDataChangedCallback()
        boundService = null
        requireContext().unbindService(mServiceConnection)
    }

    private fun handleServiceData(serviceDataType: ServiceDataType, data: String) {
        if (serviceDataType == ServiceDataType.SENSOR_DATA) {
            val pair = CalculateUtil.parseSensorCounter(data)
            if (pair?.first != null) {
                mViewModel.sensorDataValue.set(pair.first)
                mViewModel.tensionValue.set(if (pair.second == 1) "有张力" else "无张力")
            }

        }
    }

    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                boundService?.setOnDataChangedCallback { serviceDataType, data ->
                    handleServiceData(serviceDataType, data)
                }
            }

        }

        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        unBindService()
    }

    /**
     * 清除计数器数据
     */
    private fun clearSensorCount() {
        boundService?.receiveCommand(ServiceDataType.SENSOR_DATA, "set cnt 0 \r\n")
    }


    inner class ProxyClick {

        fun clear() {
            clearSensorCount()
        }

        fun done() {
            hideSoftKeyboard(requireActivity())
            try {
                val liftingHeight = binding.edittext.text.toString().toDouble()
                if (liftingHeight < 3) {
                    "提升高度最低位3米".toast()
                    return
                }
                binding.edittext.clearFocus()
                val countIntValue = mViewModel.sensorDataValue.get().toInt()
                mViewModel.liftingHeightRatio = (countIntValue / liftingHeight).toFloat()
                saveLocalData()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun saveTensionValue() {
            /*            val tensionValue = binding.tensionValue.text.toString().toFloat()
                        if (tensionValue < MIN_TENSION_VALUE) {
                            "张力传感器阈值不得低于$MIN_TENSION_VALUE, 请重新设置".toast()
                            return
                        }
                        CacheUtil.setTensionValue(tensionValue)
                        "张力传感器阈值保存成功".toast()
                        binding.tensionValue.clearFocus()*/
        }

        fun calibrate() {
            done()
        }


    }

}