package com.ztkj.vibepress.app.widget.customview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.ztkj.vibepress.R
import com.ztkj.vibepress.databinding.LayoutHeaderButtonBinding

/**
 * @CreateTime : 2023/6/12 15:11
 * <AUTHOR> AppOS
 * @Description :
 */
class HeaderButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val binding: LayoutHeaderButtonBinding

    init {
        binding = LayoutHeaderButtonBinding.inflate(LayoutInflater.from(context), this, true)
        attrs?.let { applyAttributes(it) }
    }


    private fun applyAttributes(attrs: AttributeSet) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.HeaderButton)
        val title = typedArray.getString(R.styleable.HeaderButton_title)
        title?.let { binding.title.text = it }
        val iconRes = typedArray.getResourceId(R.styleable.HeaderButton_icon, 0)
        binding.icon.setImageResource(iconRes)
        typedArray.recycle()
    }

    fun setAttribute(text: String, iconRes: Int) {
        binding.title.text = text
        binding.icon.setImageResource(iconRes)
    }

}