package com.ztkj.vibepress.ui.adapter

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ztkj.vibepress.R
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.kEnum.TampWorkType
import com.ztkj.vibepress.databinding.ItemJobStandardListBinding

/**
 * @CreateTime : 2023/6/20 16:55
 * <AUTHOR> AppOS
 * @Description :
 */
class JobStandardListAdapter :
    BaseQuickAdapter<JobStandardEntity, JobStandardListAdapter.VH>() {

    class VH(
        parent: ViewGroup,
        val binding: ItemJobStandardListBinding = ItemJobStandardListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: VH,
        position: Int,
        item: JobStandardEntity?
    ) {
        if (item == null) return
        holder.binding.run {
            name.text = item.name
            type.text = item.descTampWorkType
//            tampingEnergy.text = item.requireEnergy.toInt().toString()
            liftingTimes.text = item.requireTimes.toString()
            liftingHeight.text = item.requireDistance.toString()
            val currentJobEntity = appViewModel.jobStandardEntity.value
            setToCurrent.visibility =
                if (currentJobEntity?.uID == item.uID) View.INVISIBLE else View.VISIBLE
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): VH {
        return VH(parent)
    }

    class DividerItemDecoration(context: Context) : RecyclerView.ItemDecoration() {
        private val dividerHeight: Int =
            context.resources.getDimensionPixelSize(R.dimen.divider_height)

        private val dividerColor: Int = context.getColor(R.color.color_1D8D98_30alpha)
        private val paint: Paint = Paint()

        init {
            paint.color = dividerColor
            paint.style = Paint.Style.FILL
        }

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            outRect.set(0, 0, 0, dividerHeight)
        }

        override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
            val left = parent.paddingStart
            val right = parent.width - parent.paddingEnd
            val childCount = parent.childCount

            for (i in 0 until childCount - 1) {
                val child = parent.getChildAt(i)
                val top = child.bottom
                val bottom = top + dividerHeight
                c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), paint)
            }
        }
    }

}