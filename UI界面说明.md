# CounterFragment UI界面说明（最终优化版）

## 界面布局结构

### 1. 模式切换卡片（顶部）- 优化空间布局
```
┌─────────────────────────────────────┐
│ 计时器模式        [切换到倒数模式]   │
└─────────────────────────────────────┘
```
- **优化点**：文本和按钮放在同一行，节省垂直空间
- 左侧显示"计时器模式"标题
- 右侧显示切换按钮，文字动态变化
- 正数模式时：显示"切换到倒数模式"，蓝色背景
- 倒数模式时：显示"切换到正数模式"，灰色背景
- **间隔**：底部margin减少到16dp

### 2. 倒计时设置卡片（仅倒数模式显示）- 分钟秒数精确设置
```
┌─────────────────────────────────────┐
│ 倒计时时长：[ 1 ] 分 [ 30 ] 秒     │
└─────────────────────────────────────┘
```
- **重大优化**：支持分钟和秒的分别设置
- 两个60dp宽度的输入框，分别设置分钟和秒
- 分钟输入：0-99分钟（最大2位数字）
- 秒数输入：0-59秒（最大2位数字）
- **自动设置**：输入后立即生效，无需点击设置按钮
- **去掉设置按钮**：简化操作流程
- 输入框增加minHeight=48dp，确保正常显示
- 只在倒数模式下显示
- **间隔**：底部margin减少到16dp

### 3. 时间显示卡片（中央）- 增加间隔和视觉效果
```
┌─────────────────────────────────────┐
│              正数计时                │
│                                     │
│              00:00                  │
│                                     │
└─────────────────────────────────────┘
```
- **优化点**：增加padding到40dp，提供更好的视觉空间
- 增加minHeight=140dp，确保卡片有足够高度
- 大字体显示当前时间（52sp，增加了字体大小）
- 格式：MM:SS 或 HH:MM:SS
- 蓝色数字，等宽字体，增加letterSpacing=0.1
- 显示当前模式（正数计时/倒数计时），字体增加到16sp
- **间隔**：底部margin减少到24dp

### 4. 控制按钮（底部）- 优化间隔
```
┌─────────────────────────────────────┐
│        ┌─────────────┐              │
│        │ ▶️ 开始     │              │
│        └─────────────┘              │
└─────────────────────────────────────┘
```
- **优化点**：增加顶部margin=16dp，与时间卡片保持间隔
- 单个开始/停止按钮（220dp宽度，略微增加）：
  - 停止状态：蓝色背景，播放图标，"开始"文字
  - 运行状态：红色背景，停止图标，"停止"文字
- 移除重置按钮，简化操作
- 使用vertical_bias=0.0确保按钮靠上对齐

## 颜色方案

### 主要颜色
- **主色调**：`#2196F3` (蓝色)
- **危险色**：`#F84728` (红色)
- **背景色**：`#F5F5F5` (浅灰)
- **卡片背景**：`#FFFFFF` (白色)
- **次要背景**：`#EEEEEE` (灰色)

### 状态颜色
- **选中状态**：主色调背景 + 白色文字
- **未选中状态**：灰色背景 + 主色调文字
- **运行状态**：红色背景
- **禁用状态**：灰色

## 交互效果

### 按钮状态变化（优化后）
1. **模式切换**：
   - 点击时立即切换视觉状态和文字
   - 重置计时器到初始状态
   - 倒数模式显示/隐藏设置卡片
   - 按钮颜色在蓝色和灰色间切换

2. **开始/停止**：
   - 颜色从蓝色变为红色
   - 图标从播放变为停止
   - 文字从"开始"变为"停止"
   - 停止时自动重置计时器

### 动画效果
- 按钮点击有轻微的触摸反馈
- 卡片有阴影效果
- 时间数字实时更新（每100ms）

## 响应式设计

### 布局适配
- 使用ConstraintLayout确保各种屏幕尺寸适配
- 按钮使用权重分布，自动适应宽度
- 卡片间距和内边距按比例调整

### 文字大小
- 标题：18sp
- 正文：16sp
- 时间显示：48sp（大字体）
- 按钮文字：18sp

## 用户体验特点（优化后）

1. **简化操作**：减少按钮数量，操作更直观
2. **状态反馈**：每个操作都有明确的视觉反馈
3. **智能布局**：固定输入框宽度，避免布局问题
4. **一键切换**：模式切换更加便捷
5. **信息提示**：通过Toast显示操作结果
6. **一致性**：遵循Material Design规范
