plugins {
    id 'com.android.library'
}

android {
    compileSdk 32

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    defaultConfig {
        minSdk 23
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.ztkj.sensorlib'
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.5.0'
    implementation 'com.google.android.material:material:1.6.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    //基础库
    implementation project(path: ':baseLib')
    //串口库
    implementation 'io.github.xmaihh:serialport:2.1.1'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'com.licheedev:android-serialport:2.1.3'
    implementation  files('libs/qyled.jar')

}