<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.setting.AboutFragment.ProxyClick" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.AboutFragment">


        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolbar"
            app:centerTitle="@string/about"
            app:showRightBtn="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            android:layout_marginTop="32dp"
            android:layout_width="match_parent"
            android:showDividers="beginning|middle|end"
            android:divider="@drawable/shape_divider_linear"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="40dp"
            android:background="#1A2FC1FF"
            android:orientation="vertical">

            <TextView
                style="@style/SettingTextView"
                android:onClick="@{ () -> click.advancedSystemSetting()}"
                android:text="@string/advancedSystemSetting"
                app:drawableStartCompat="@drawable/ic_setting_normal" />

            <TextView
                style="@style/SettingTextView"
                android:onClick="@{ () -> click.exitLogin()}"
                android:text="@string/exit_login"
                app:drawableStartCompat="@drawable/ic_exit" />

            <TextView
                style="@style/SettingTextView"
                android:id="@+id/tv_version"
                android:onClick="@{ () -> click.checkUpdate()}"
                android:text="@string/checkUpdate"
                app:drawableStartCompat="@drawable/ic_checkupdate" />
        </LinearLayout>

        <!--        <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="60dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/toolbar"
                    app:srcCompat="@mipmap/about_bg" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="60dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@mipmap/about_2" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>