package com.ztkj.vibepress.ui.fragment.setting

import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.util.setOnclickNoRepeat
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.hideSoftKeyboard
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.data.model.bean.NetConfigBean
import com.ztkj.vibepress.databinding.FragmentNetParamBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class NetParamFragment : BaseFragment<BaseViewModel, FragmentNetParamBinding>() {

    private lateinit var netConfigBean: NetConfigBean

    override fun initView(savedInstanceState: Bundle?) {
        initToolbar()
    }

    private fun initToolbar() {
        binding.toolbar.run {
            initNormalClose(this@NetParamFragment)
            setOnclickNoRepeat(this) {
                saveLocalData()
            }
        }
    }

    override fun lazyLoadData() {
        super.lazyLoadData()

        lifecycleScope.launch {
            netConfigBean = CacheUtil.getNetworkConfig()
            withContext(Dispatchers.Main) {
                binding.config = netConfigBean
            }
        }
    }

    private fun saveLocalData() {
        hideSoftKeyboard(mActivity)
        if (netConfigBean.isValid()) {
            CacheUtil.setNetworkConfig(netConfigBean)
            getString(R.string.save_success).toast()
        } else {
            "请检查设置，所有属性不能为空".toast()
        }

    }
}
