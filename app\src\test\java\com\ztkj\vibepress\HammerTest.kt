package com.ztkj.vibepress

import com.ztkj.vibepress.app.util.CalculateUtil
import org.junit.Test

/**
 * @CreateTime : 2023/5/11 15:52
 * <AUTHOR> AppOS
 * @Description :
 */
class HammerTest {

    @Test
    fun test_hammer() {
        val amplitudeMax = 25 //波峰

        val amplitudeMin = -4 //波谷

        val period = 150 //数量

        var step = 1
        var sensorValue = 0 //计数器值

        var tensionState = 0 ////张力状态（0:无张力；1:有张力）
        for (i in 0 until period) {
            tensionState =
                if (step == 1 && sensorValue > amplitudeMin + 2 && sensorValue < amplitudeMax - 2) {
                    1
                } else {
                    0
                }
            //处理传感器数据
            println("原始数据: 计数器值：$sensorValue,张力状态:$tensionState")
            CalculateUtil.processSensorData("$sensorValue,$tensionState")
            if (sensorValue == amplitudeMax) {
                step = -1
            } else if (sensorValue == amplitudeMin) {
                step = 1
            }
            sensorValue += step
        }
    }
}