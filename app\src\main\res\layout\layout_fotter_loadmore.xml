<?xml version="1.0" encoding="utf-8"?><!--
    Copyright 2017 <PERSON>

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android">

    <ProgressBar
        android:id="@+id/loading_view"
        style="?android:attr/progressBarStyleInverse"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="6dp"
        android:textColor="@color/colorGray"
        android:textSize="13dp"
        android:visibility="gone" />

</merge>