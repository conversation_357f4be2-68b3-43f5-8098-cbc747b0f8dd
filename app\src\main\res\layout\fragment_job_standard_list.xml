<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.JobStandardListFragment">

        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolBar"
            app:centerTitle="@string/device_attribute_list"
            app:showRightBtn="true"
            app:rightBtnTitle="@string/create"
            app:rightBtnIcon="@drawable/ic_create"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvData"
            android:layout_marginTop="10dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_job_standard_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>