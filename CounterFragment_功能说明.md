# CounterFragment 秒表计时器功能说明（优化版）

## 功能概述

为CounterFragment添加了一个符合Material Design设计规范的秒表计时器功能，支持正数计时和倒数计时两种模式。经过UI优化，界面更加简洁直观。

## 主要功能

### 1. 计时器模式
- **正数模式**：从0开始向上计时
- **倒数模式**：从设定时间开始向下倒计时
- **一键切换**：通过单个按钮在两种模式间切换

### 2. 计时器控制
- **开始/停止**：单个按钮控制计时器的运行状态
- **智能重置**：停止时自动重置到初始状态
- **时长设置**：在倒数模式下可以设置倒计时时长（分钟）

### 3. 接口调用模拟
- 计时器开始时调用后台接口（模拟）
- 计时器停止时调用后台接口（模拟）
- 倒计时结束时调用后台接口（模拟）
- 通过Toast显示接口调用状态

## UI设计特点（优化后）

### Material Design 元素
- 使用MaterialCardView作为容器
- MaterialButton用于所有按钮
- TextInputLayout用于输入框（固定宽度80dp）
- 圆角设计和阴影效果
- 符合Material Design颜色规范

### 简化的响应式设计
- 单个模式切换按钮，文字动态变化
- 倒数模式时显示时长设置区域
- 单个开始/停止按钮，颜色和图标动态变化
- 移除重置按钮，简化操作流程
- 输入框宽度固定，避免布局问题

## 技术实现

### 架构模式
- **MVVM架构**：使用ViewModel管理业务逻辑
- **DataBinding**：实现数据和UI的双向绑定
- **LiveData**：观察数据变化并更新UI

### 核心组件

#### CounterStateViewModel
- 管理计时器状态和业务逻辑
- 使用Kotlin协程实现计时功能
- 提供格式化时间显示的方法
- 模拟后台接口调用

#### CounterFragment
- 处理UI交互和状态更新
- 实现按钮样式的动态变化
- 观察ViewModel数据变化
- 显示Toast消息

#### fragment_counter.xml
- Material Design布局设计
- 使用ConstraintLayout实现响应式布局
- DataBinding表达式绑定数据

## 使用方法（优化后）

1. **切换模式**：点击模式切换按钮在正数/倒数模式间切换
2. **设置倒计时**：在倒数模式下，输入分钟数并点击"设置"
3. **开始计时**：点击"开始"按钮启动计时器
4. **停止计时**：点击"停止"按钮停止计时器（自动重置）

## 文件修改清单

### 新增文件
- `app/src/main/res/drawable/ic_play.xml` - 播放图标
- `app/src/main/res/drawable/ic_stop.xml` - 停止图标
- `app/src/main/res/drawable/ic_refresh.xml` - 刷新图标

### 修改文件
- `app/src/main/java/com/ztkj/vibepress/viewmodel/state/CounterStateViewModel.kt` - 添加计时器业务逻辑
- `app/src/main/java/com/ztkj/vibepress/ui/fragment/home/<USER>
- `app/src/main/res/layout/fragment_counter.xml` - 重新设计UI布局

## 特色功能（优化后）

1. **实时更新**：计时器每100ms更新一次，提供流畅的显示效果
2. **状态管理**：完善的状态管理，防止内存泄漏
3. **简化操作**：减少按钮数量，操作更直观
4. **智能布局**：固定输入框宽度，避免布局问题
5. **一键切换**：模式切换更加便捷
6. **扩展性**：易于扩展和修改的代码结构
7. **兼容性**：保持与现有框架的完全兼容

## 注意事项

- 计时器使用协程实现，在Fragment销毁时会自动清理
- 所有接口调用都是模拟实现，实际项目中需要替换为真实的API调用
- UI样式完全符合项目现有的Material Design规范
- 保持了项目原有的DataBinding和ViewModel架构模式
