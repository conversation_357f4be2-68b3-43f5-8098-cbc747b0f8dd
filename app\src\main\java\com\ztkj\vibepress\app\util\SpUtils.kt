package com.ztkj.vibepress.app.util

import android.content.Context
import android.util.Base64
import com.tencent.mmkv.MMKV
import java.io.*

/**
 * @CreateTime : 2022/10/17 11:34
 * <AUTHOR> AppOS
 * @Description :
 */
object SpUtils {
    private lateinit var mmkv: MMKV

    public fun init(context: Context) {
        MMKV.initialize(context)
        mmkv = MMKV.defaultMMKV()
    }

    fun putString(key: String, value: String) {
        mmkv.putString(key,value)
    }

    fun putInt(key: String, value: Int) {
        mmkv.putInt(key,value)
    }

    fun putBoolean(key: String, value: Boolean) {
        mmkv.putBoolean(key,value)
    }

    fun putBytes(key: String,value: ByteArray) {
        mmkv.putBytes(key,value)
    }

    fun putFloat(key: String,value: Float) {
        mmkv.putFloat(key,value)
    }

    fun putLong(key: String,value: Long) {
        mmkv.putLong(key,value)
    }

    fun putStringSet(key: String,value: Set<String>) {
        mmkv.putStringSet(key,value)
    }

    /**
     * 存储对象(对象必须可序列化)
     */
    fun putObject(key: String,value: Any?) {
       value?.let {
           try {
               val outputStream = ByteArrayOutputStream()
               val objectOutputStream = ObjectOutputStream(outputStream)
               objectOutputStream.writeObject(value)
               // 将对象放到OutputStream中
               // 将对象转换成byte数组，并将其进行base64编码
               val string = String(Base64.encode(outputStream.toByteArray(), Base64.DEFAULT))
               outputStream.close()
               objectOutputStream.close()
               putString(key,string)
           }catch (e: IOException){
               e.printStackTrace()
           }
       }
    }

    @JvmStatic
    fun getString(key: String,defaultValue: String):String? = mmkv.getString(key,defaultValue)

    fun getInt(key: String, defaultValue: Int): Int =   mmkv.getInt(key, defaultValue)

    fun getLong(key: String, defaultValue: Long): Long = mmkv.getLong(key, defaultValue)

    fun getBytes(key: String, defaultValue: ByteArray): ByteArray = mmkv.getBytes(key, defaultValue)

    fun getFloat(key: String?, defaultValue: Float): Float = mmkv.getFloat(key, defaultValue)

    fun getBoolean(key: String?, defaultValue: Boolean): Boolean = mmkv.getBoolean(key, defaultValue)

    fun getStringSet(key: String, defaultValue: Set<String>): MutableSet<String>? = mmkv.getStringSet(key,defaultValue)

    /**
     * 获取对象
     */
    fun getObject(key: String): Any? {
        val wordBase64 = getString(key, "")
        wordBase64.takeUnless { it.isNullOrBlank() }?.let {
            try {
                val objBytes = Base64.decode(wordBase64!!.toByteArray(), Base64.DEFAULT)
                val bais = ByteArrayInputStream(objBytes)
                val ois = ObjectInputStream(bais)
                // 将byte数组转换成product对象
                val obj = ois.readObject()
                bais.close()
                ois.close()
                return obj
            } catch (e: IOException) {
                e.printStackTrace()
            } catch (e: ClassNotFoundException) {
                e.printStackTrace()
            }
        }
        return null
    }

    @Suppress("UNUSED_PARAMETER","UNCHECKED_CAST")
    fun <T> getObject(key: String,t: Class<T>): T? {
        return getObject(key) as T
    }

    fun removeValueForKey(key: String) {
        mmkv.removeValueForKey(key)
    }

    /**
     * 清除所有SharedPreference
     */
    fun clearAll() {
        mmkv.clearAll()
    }



}