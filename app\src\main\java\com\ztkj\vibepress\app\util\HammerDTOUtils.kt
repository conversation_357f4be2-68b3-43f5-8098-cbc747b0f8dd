package com.ztkj.vibepress.app.util

import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.bean.HammerDataDTO

/**
 * @CreateTime : 2023/5/17 20:22
 * <AUTHOR> AppOS
 * @Description :
 */
object HammerDTOUtils {

    /**
     * .append("计数器值")
    .append(segSeparator)
    .append("锤高")
    .append(segSeparator)
    .append("锤状态")
    .append(segSeparator)
    .append("张力状态")
    .append(segSeparator)
    .append("张力变化事件")
     */
    @JvmStatic
    fun parseHammerToNettyStr(): String? {
        return appViewModel.hammerDataDTO.value?.run {
            "$tensionValue,$hammerHeight,$hammerState,$tensionState,$tensionChange"
        }

    }

    @JvmStatic
    fun getHammerValue(): HammerDataDTO = appViewModel.hammerDataDTO.value ?: HammerDataDTO()


}