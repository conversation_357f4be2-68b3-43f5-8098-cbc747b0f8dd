package com.ztkj.imext.im.handler;

import com.ztkj.imext.bean.AppMessage;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMessageHandler.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im.handler</p>
 * <b>
 * <p>@Description:     类描述</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/10 03:41</p>
 * <p>@email:           chens<PERSON><PERSON>@outlook.com</p>
 */
public interface IMessageHandler {

    void execute(AppMessage message);
}
