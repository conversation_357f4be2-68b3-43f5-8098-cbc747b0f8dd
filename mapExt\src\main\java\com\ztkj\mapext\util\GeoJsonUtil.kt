package com.ztkj.mapext.util

import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.geometry.SpatialReference
import com.esri.arcgisruntime.geometry.SpatialReferences
import com.esri.arcgisruntime.mapping.view.Graphic
import com.ztkj.app.mapcore.entity.GeoJsonModel
import com.ztkj.mapext.util.JsonUtils.objToMap

/**
 * @CreateTime : 2023/7/25 14:34
 * <AUTHOR> AppOS
 * @Description :
 */
object GeoJsonUtil {

    fun parseGeoJson(geoJsonModel: GeoJsonModel?): List<Graphic>? {
        if (geoJsonModel == null) {
            return null
        }
        if (geoJsonModel.type != "FeatureCollection") {
            return null
        }

        val list: MutableList<Graphic> = ArrayList()
        for (feature in geoJsonModel.features) {
            val type = feature.geometry.type
            if (GeoJsonModel.GeometryType.Point == type) {
                val graphic: Graphic = createGraphic(feature)
                list.add(graphic)
            }
        }
        return list
    }

    private fun createGraphic(feature: GeoJsonModel.FeaturesModel): Graphic {
        //获取点坐标的集合
        val pointList = feature.geometry.coordinates as List<Double>
        //根据坐标和坐标系生成点集合对象
        val geometry = Point(pointList[0], pointList[1], SpatialReference.create(4490))
        //点符号
        val attribute = objToMap(feature.properties)
        return if (attribute == null) {
            val symbol = SymbolUtil.buildTampingPointSymbol("0")
            Graphic(geometry, symbol)
        } else {
            val symbol = SymbolUtil.buildTampingPointSymbol(attribute["hit_count"].toString())
            Graphic(geometry, attribute, symbol)
        }
    }
}