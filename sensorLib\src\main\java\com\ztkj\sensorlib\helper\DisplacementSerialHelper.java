package com.ztkj.sensorlib.helper;

import android.serialport.SerialPort;
import android.serialport.SerialPortFinder;
import android.util.Log;

import com.ztkj.sensorlib.utils.ByteUtil;
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Timer;
import java.util.TimerTask;

import tp.xmaihh.serialport.SerialHelper;
import tp.xmaihh.serialport.bean.ComBean;

/**
 * @CreateTime : 2022/9/5 10:13
 * <AUTHOR> AppOS
 * @Description : 位移传感器Helper
 */
public class DisplacementSerialHelper  {
    private SerialHelper serialHelper;
    private Timer mTimer;
    private static final String TAG = "DisplacementSerialHelpe";

    public interface OnDataReceivedCallback{
        void onDataReceived(NmeaAnalysisUtil nmeaAnalysisUtil);
    }


    public DisplacementSerialHelper(String sPort, int iBaudRate,int type,OnDataReceivedCallback callback) {
         serialHelper = new SerialHelper(sPort,iBaudRate) {
            @Override
            protected void onDataReceived(ComBean comBean) {
                switch (SensorManager.getInstance().getSensorType()){
                    case ZNYS:
                        handleZNYSData(callback,comBean,type);
                        break;
                    case XBJ:
                        handleXBJData(callback,comBean,type);
                        break;
                }

            }
        };
    }

    private void handleZNYSData(OnDataReceivedCallback callback,ComBean comBean,int type){
        if (callback != null){
            String _data = new String(comBean.bRec, StandardCharsets.UTF_8);
            NmeaAnalysisUtil nmeaAnalysisUtil = NmeaAnalysisUtil.getInstance();
            nmeaAnalysisUtil.processNmeaData(_data);
            callback.onDataReceived(nmeaAnalysisUtil);
//           if (type  == 1){
//               Log.e(TAG, "handleZNYSData: " + _data );
//           }
        }
    }

    private void handleXBJData(OnDataReceivedCallback callback,ComBean comBean,int type){
        if (callback != null){
//            Log.e(TAG, "传感器收到数据: "+ comBean.sComPort +", " + comBean.sRecTime +", " + new String(comBean.bRec, StandardCharsets.UTF_8) );
            if (comBean.bRec.length >= 9 ){
                byte[] subBytes = new byte[4];
                System.arraycopy(comBean.bRec, 3, subBytes, 0, subBytes.length);
                float hexFloat = ByteUtil.bytesHexToFloat(subBytes);
                NmeaAnalysisUtil nmeaAnalysisUtil = NmeaAnalysisUtil.getInstance();
                nmeaAnalysisUtil.processNmeaData("!DSH" + type + hexFloat);
                callback.onDataReceived(nmeaAnalysisUtil);
            }

        }
    }

    public String getPort(){
        if (serialHelper == null){
            return  "";
        }
        return serialHelper.getPort();
    }

    public boolean isOpen(){
        if (serialHelper == null){
            return false;
        }
        return serialHelper.isOpen();
    }

    public void open() throws IOException {
        serialHelper.open();
        startService();
    }

    public void close(){
        serialHelper.close();
        if (mTimer != null) {
            mTimer.cancel();
        }
    }

    public void sendHex(String sHex){
        serialHelper.sendHex(sHex);
    }

    public void send(byte[] bOutArray){
        serialHelper.send(bOutArray);
    }

    public void sendTxt(String sTxt){
        serialHelper.sendTxt(sTxt);
    }


    public void startService(){
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
//                serialHelper.sendTxt("?T\n");
                switch (SensorManager.getInstance().getSensorType()){
                    case ZNYS:
                        byte[] bytes = new byte[]{63, 84, 13, 10};
                        serialHelper.send(bytes);
                        break;
                    case XBJ:
                        serialHelper.sendHex("02030008000245FA");
                        break;
                }
            }
        },1000L,1000L);
    }

    public void stopService(){
        mTimer.cancel();
    }
}
