// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.proto

package com.ztkj.im.protobuf;

public final class MessageProtobuf {
  private MessageProtobuf() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 消息头
     * </pre>
     *
     * <code>.Head head = 1;</code>
     * @return Whether the head field is set.
     */
    boolean hasHead();
    /**
     * <pre>
     * 消息头
     * </pre>
     *
     * <code>.Head head = 1;</code>
     * @return The head.
     */
    com.ztkj.im.protobuf.MessageProtobuf.Head getHead();
    /**
     * <pre>
     * 消息头
     * </pre>
     *
     * <code>.Head head = 1;</code>
     */
    com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder getHeadOrBuilder();

    /**
     * <pre>
     * 消息体
     * </pre>
     *
     * <code>string body = 2;</code>
     * @return The body.
     */
    java.lang.String getBody();
    /**
     * <pre>
     * 消息体
     * </pre>
     *
     * <code>string body = 2;</code>
     * @return The bytes for body.
     */
    com.google.protobuf.ByteString
        getBodyBytes();
  }
  /**
   * Protobuf type {@code Msg}
   */
  public static final class Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Msg)
      MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Msg.newBuilder() to construct.
    private Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Msg() {
      body_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Msg();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.ztkj.im.protobuf.MessageProtobuf.Msg.class, com.ztkj.im.protobuf.MessageProtobuf.Msg.Builder.class);
    }

    public static final int HEAD_FIELD_NUMBER = 1;
    private com.ztkj.im.protobuf.MessageProtobuf.Head head_;
    /**
     * <pre>
     * 消息头
     * </pre>
     *
     * <code>.Head head = 1;</code>
     * @return Whether the head field is set.
     */
    @java.lang.Override
    public boolean hasHead() {
      return head_ != null;
    }
    /**
     * <pre>
     * 消息头
     * </pre>
     *
     * <code>.Head head = 1;</code>
     * @return The head.
     */
    @java.lang.Override
    public com.ztkj.im.protobuf.MessageProtobuf.Head getHead() {
      return head_ == null ? com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance() : head_;
    }
    /**
     * <pre>
     * 消息头
     * </pre>
     *
     * <code>.Head head = 1;</code>
     */
    @java.lang.Override
    public com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder getHeadOrBuilder() {
      return head_ == null ? com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance() : head_;
    }

    public static final int BODY_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object body_ = "";
    /**
     * <pre>
     * 消息体
     * </pre>
     *
     * <code>string body = 2;</code>
     * @return The body.
     */
    @java.lang.Override
    public java.lang.String getBody() {
      java.lang.Object ref = body_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        body_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 消息体
     * </pre>
     *
     * <code>string body = 2;</code>
     * @return The bytes for body.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBodyBytes() {
      java.lang.Object ref = body_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        body_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (head_ != null) {
        output.writeMessage(1, getHead());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(body_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, body_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (head_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHead());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(body_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, body_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.ztkj.im.protobuf.MessageProtobuf.Msg)) {
        return super.equals(obj);
      }
      com.ztkj.im.protobuf.MessageProtobuf.Msg other = (com.ztkj.im.protobuf.MessageProtobuf.Msg) obj;

      if (hasHead() != other.hasHead()) return false;
      if (hasHead()) {
        if (!getHead()
            .equals(other.getHead())) return false;
      }
      if (!getBody()
          .equals(other.getBody())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHead()) {
        hash = (37 * hash) + HEAD_FIELD_NUMBER;
        hash = (53 * hash) + getHead().hashCode();
      }
      hash = (37 * hash) + BODY_FIELD_NUMBER;
      hash = (53 * hash) + getBody().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.ztkj.im.protobuf.MessageProtobuf.Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Msg)
        com.ztkj.im.protobuf.MessageProtobuf.MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.ztkj.im.protobuf.MessageProtobuf.Msg.class, com.ztkj.im.protobuf.MessageProtobuf.Msg.Builder.class);
      }

      // Construct using com.ztkj.im.protobuf.MessageProtobuf.Msg.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        head_ = null;
        if (headBuilder_ != null) {
          headBuilder_.dispose();
          headBuilder_ = null;
        }
        body_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Msg_descriptor;
      }

      @java.lang.Override
      public com.ztkj.im.protobuf.MessageProtobuf.Msg getDefaultInstanceForType() {
        return com.ztkj.im.protobuf.MessageProtobuf.Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.ztkj.im.protobuf.MessageProtobuf.Msg build() {
        com.ztkj.im.protobuf.MessageProtobuf.Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.ztkj.im.protobuf.MessageProtobuf.Msg buildPartial() {
        com.ztkj.im.protobuf.MessageProtobuf.Msg result = new com.ztkj.im.protobuf.MessageProtobuf.Msg(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.ztkj.im.protobuf.MessageProtobuf.Msg result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.head_ = headBuilder_ == null
              ? head_
              : headBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.body_ = body_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.ztkj.im.protobuf.MessageProtobuf.Msg) {
          return mergeFrom((com.ztkj.im.protobuf.MessageProtobuf.Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.ztkj.im.protobuf.MessageProtobuf.Msg other) {
        if (other == com.ztkj.im.protobuf.MessageProtobuf.Msg.getDefaultInstance()) return this;
        if (other.hasHead()) {
          mergeHead(other.getHead());
        }
        if (!other.getBody().isEmpty()) {
          body_ = other.body_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getHeadFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                body_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.ztkj.im.protobuf.MessageProtobuf.Head head_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.ztkj.im.protobuf.MessageProtobuf.Head, com.ztkj.im.protobuf.MessageProtobuf.Head.Builder, com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder> headBuilder_;
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       * @return Whether the head field is set.
       */
      public boolean hasHead() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       * @return The head.
       */
      public com.ztkj.im.protobuf.MessageProtobuf.Head getHead() {
        if (headBuilder_ == null) {
          return head_ == null ? com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance() : head_;
        } else {
          return headBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      public Builder setHead(com.ztkj.im.protobuf.MessageProtobuf.Head value) {
        if (headBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          head_ = value;
        } else {
          headBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      public Builder setHead(
          com.ztkj.im.protobuf.MessageProtobuf.Head.Builder builderForValue) {
        if (headBuilder_ == null) {
          head_ = builderForValue.build();
        } else {
          headBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      public Builder mergeHead(com.ztkj.im.protobuf.MessageProtobuf.Head value) {
        if (headBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            head_ != null &&
            head_ != com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance()) {
            getHeadBuilder().mergeFrom(value);
          } else {
            head_ = value;
          }
        } else {
          headBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      public Builder clearHead() {
        bitField0_ = (bitField0_ & ~0x00000001);
        head_ = null;
        if (headBuilder_ != null) {
          headBuilder_.dispose();
          headBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      public com.ztkj.im.protobuf.MessageProtobuf.Head.Builder getHeadBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      public com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder getHeadOrBuilder() {
        if (headBuilder_ != null) {
          return headBuilder_.getMessageOrBuilder();
        } else {
          return head_ == null ?
              com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance() : head_;
        }
      }
      /**
       * <pre>
       * 消息头
       * </pre>
       *
       * <code>.Head head = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.ztkj.im.protobuf.MessageProtobuf.Head, com.ztkj.im.protobuf.MessageProtobuf.Head.Builder, com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder> 
          getHeadFieldBuilder() {
        if (headBuilder_ == null) {
          headBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.ztkj.im.protobuf.MessageProtobuf.Head, com.ztkj.im.protobuf.MessageProtobuf.Head.Builder, com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder>(
                  getHead(),
                  getParentForChildren(),
                  isClean());
          head_ = null;
        }
        return headBuilder_;
      }

      private java.lang.Object body_ = "";
      /**
       * <pre>
       * 消息体
       * </pre>
       *
       * <code>string body = 2;</code>
       * @return The body.
       */
      public java.lang.String getBody() {
        java.lang.Object ref = body_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          body_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 消息体
       * </pre>
       *
       * <code>string body = 2;</code>
       * @return The bytes for body.
       */
      public com.google.protobuf.ByteString
          getBodyBytes() {
        java.lang.Object ref = body_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          body_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 消息体
       * </pre>
       *
       * <code>string body = 2;</code>
       * @param value The body to set.
       * @return This builder for chaining.
       */
      public Builder setBody(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        body_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息体
       * </pre>
       *
       * <code>string body = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBody() {
        body_ = getDefaultInstance().getBody();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息体
       * </pre>
       *
       * <code>string body = 2;</code>
       * @param value The bytes for body to set.
       * @return This builder for chaining.
       */
      public Builder setBodyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        body_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Msg)
    }

    // @@protoc_insertion_point(class_scope:Msg)
    private static final com.ztkj.im.protobuf.MessageProtobuf.Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.ztkj.im.protobuf.MessageProtobuf.Msg();
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Msg>
        PARSER = new com.google.protobuf.AbstractParser<Msg>() {
      @java.lang.Override
      public Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.ztkj.im.protobuf.MessageProtobuf.Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface HeadOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Head)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 消息id
     * </pre>
     *
     * <code>string msgId = 1;</code>
     * @return The msgId.
     */
    java.lang.String getMsgId();
    /**
     * <pre>
     * 消息id
     * </pre>
     *
     * <code>string msgId = 1;</code>
     * @return The bytes for msgId.
     */
    com.google.protobuf.ByteString
        getMsgIdBytes();

    /**
     * <pre>
     * 消息类型
     * </pre>
     *
     * <code>int32 msgType = 2;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <pre>
     * 消息内容类型
     * </pre>
     *
     * <code>int32 msgContentType = 3;</code>
     * @return The msgContentType.
     */
    int getMsgContentType();

    /**
     * <pre>
     * 消息发送者id
     * </pre>
     *
     * <code>string fromId = 4;</code>
     * @return The fromId.
     */
    java.lang.String getFromId();
    /**
     * <pre>
     * 消息发送者id
     * </pre>
     *
     * <code>string fromId = 4;</code>
     * @return The bytes for fromId.
     */
    com.google.protobuf.ByteString
        getFromIdBytes();

    /**
     * <pre>
     * 消息接收者id
     * </pre>
     *
     * <code>string toId = 5;</code>
     * @return The toId.
     */
    java.lang.String getToId();
    /**
     * <pre>
     * 消息接收者id
     * </pre>
     *
     * <code>string toId = 5;</code>
     * @return The bytes for toId.
     */
    com.google.protobuf.ByteString
        getToIdBytes();

    /**
     * <pre>
     * 消息时间戳
     * </pre>
     *
     * <code>int64 timestamp = 6;</code>
     * @return The timestamp.
     */
    long getTimestamp();

    /**
     * <pre>
     * 状态报告
     * </pre>
     *
     * <code>int32 statusReport = 7;</code>
     * @return The statusReport.
     */
    int getStatusReport();

    /**
     * <pre>
     * 扩展字段，以key/value形式存放的json
     * </pre>
     *
     * <code>string extend = 8;</code>
     * @return The extend.
     */
    java.lang.String getExtend();
    /**
     * <pre>
     * 扩展字段，以key/value形式存放的json
     * </pre>
     *
     * <code>string extend = 8;</code>
     * @return The bytes for extend.
     */
    com.google.protobuf.ByteString
        getExtendBytes();
  }
  /**
   * Protobuf type {@code Head}
   */
  public static final class Head extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Head)
      HeadOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Head.newBuilder() to construct.
    private Head(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Head() {
      msgId_ = "";
      fromId_ = "";
      toId_ = "";
      extend_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Head();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Head_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Head_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.ztkj.im.protobuf.MessageProtobuf.Head.class, com.ztkj.im.protobuf.MessageProtobuf.Head.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object msgId_ = "";
    /**
     * <pre>
     * 消息id
     * </pre>
     *
     * <code>string msgId = 1;</code>
     * @return The msgId.
     */
    @java.lang.Override
    public java.lang.String getMsgId() {
      java.lang.Object ref = msgId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        msgId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 消息id
     * </pre>
     *
     * <code>string msgId = 1;</code>
     * @return The bytes for msgId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgIdBytes() {
      java.lang.Object ref = msgId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msgId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MSGTYPE_FIELD_NUMBER = 2;
    private int msgType_ = 0;
    /**
     * <pre>
     * 消息类型
     * </pre>
     *
     * <code>int32 msgType = 2;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int MSGCONTENTTYPE_FIELD_NUMBER = 3;
    private int msgContentType_ = 0;
    /**
     * <pre>
     * 消息内容类型
     * </pre>
     *
     * <code>int32 msgContentType = 3;</code>
     * @return The msgContentType.
     */
    @java.lang.Override
    public int getMsgContentType() {
      return msgContentType_;
    }

    public static final int FROMID_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fromId_ = "";
    /**
     * <pre>
     * 消息发送者id
     * </pre>
     *
     * <code>string fromId = 4;</code>
     * @return The fromId.
     */
    @java.lang.Override
    public java.lang.String getFromId() {
      java.lang.Object ref = fromId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fromId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 消息发送者id
     * </pre>
     *
     * <code>string fromId = 4;</code>
     * @return The bytes for fromId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFromIdBytes() {
      java.lang.Object ref = fromId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fromId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOID_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object toId_ = "";
    /**
     * <pre>
     * 消息接收者id
     * </pre>
     *
     * <code>string toId = 5;</code>
     * @return The toId.
     */
    @java.lang.Override
    public java.lang.String getToId() {
      java.lang.Object ref = toId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        toId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 消息接收者id
     * </pre>
     *
     * <code>string toId = 5;</code>
     * @return The bytes for toId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getToIdBytes() {
      java.lang.Object ref = toId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        toId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 6;
    private long timestamp_ = 0L;
    /**
     * <pre>
     * 消息时间戳
     * </pre>
     *
     * <code>int64 timestamp = 6;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int STATUSREPORT_FIELD_NUMBER = 7;
    private int statusReport_ = 0;
    /**
     * <pre>
     * 状态报告
     * </pre>
     *
     * <code>int32 statusReport = 7;</code>
     * @return The statusReport.
     */
    @java.lang.Override
    public int getStatusReport() {
      return statusReport_;
    }

    public static final int EXTEND_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object extend_ = "";
    /**
     * <pre>
     * 扩展字段，以key/value形式存放的json
     * </pre>
     *
     * <code>string extend = 8;</code>
     * @return The extend.
     */
    @java.lang.Override
    public java.lang.String getExtend() {
      java.lang.Object ref = extend_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extend_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 扩展字段，以key/value形式存放的json
     * </pre>
     *
     * <code>string extend = 8;</code>
     * @return The bytes for extend.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getExtendBytes() {
      java.lang.Object ref = extend_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extend_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(msgId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, msgId_);
      }
      if (msgType_ != 0) {
        output.writeInt32(2, msgType_);
      }
      if (msgContentType_ != 0) {
        output.writeInt32(3, msgContentType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(fromId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, fromId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(toId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, toId_);
      }
      if (timestamp_ != 0L) {
        output.writeInt64(6, timestamp_);
      }
      if (statusReport_ != 0) {
        output.writeInt32(7, statusReport_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extend_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, extend_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(msgId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, msgId_);
      }
      if (msgType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, msgType_);
      }
      if (msgContentType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, msgContentType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(fromId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, fromId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(toId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, toId_);
      }
      if (timestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, timestamp_);
      }
      if (statusReport_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, statusReport_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extend_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, extend_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.ztkj.im.protobuf.MessageProtobuf.Head)) {
        return super.equals(obj);
      }
      com.ztkj.im.protobuf.MessageProtobuf.Head other = (com.ztkj.im.protobuf.MessageProtobuf.Head) obj;

      if (!getMsgId()
          .equals(other.getMsgId())) return false;
      if (getMsgType()
          != other.getMsgType()) return false;
      if (getMsgContentType()
          != other.getMsgContentType()) return false;
      if (!getFromId()
          .equals(other.getFromId())) return false;
      if (!getToId()
          .equals(other.getToId())) return false;
      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (getStatusReport()
          != other.getStatusReport()) return false;
      if (!getExtend()
          .equals(other.getExtend())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgId().hashCode();
      hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getMsgType();
      hash = (37 * hash) + MSGCONTENTTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getMsgContentType();
      hash = (37 * hash) + FROMID_FIELD_NUMBER;
      hash = (53 * hash) + getFromId().hashCode();
      hash = (37 * hash) + TOID_FIELD_NUMBER;
      hash = (53 * hash) + getToId().hashCode();
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestamp());
      hash = (37 * hash) + STATUSREPORT_FIELD_NUMBER;
      hash = (53 * hash) + getStatusReport();
      hash = (37 * hash) + EXTEND_FIELD_NUMBER;
      hash = (53 * hash) + getExtend().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ztkj.im.protobuf.MessageProtobuf.Head parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.ztkj.im.protobuf.MessageProtobuf.Head prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Head}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Head)
        com.ztkj.im.protobuf.MessageProtobuf.HeadOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Head_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Head_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.ztkj.im.protobuf.MessageProtobuf.Head.class, com.ztkj.im.protobuf.MessageProtobuf.Head.Builder.class);
      }

      // Construct using com.ztkj.im.protobuf.MessageProtobuf.Head.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        msgId_ = "";
        msgType_ = 0;
        msgContentType_ = 0;
        fromId_ = "";
        toId_ = "";
        timestamp_ = 0L;
        statusReport_ = 0;
        extend_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.ztkj.im.protobuf.MessageProtobuf.internal_static_Head_descriptor;
      }

      @java.lang.Override
      public com.ztkj.im.protobuf.MessageProtobuf.Head getDefaultInstanceForType() {
        return com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance();
      }

      @java.lang.Override
      public com.ztkj.im.protobuf.MessageProtobuf.Head build() {
        com.ztkj.im.protobuf.MessageProtobuf.Head result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.ztkj.im.protobuf.MessageProtobuf.Head buildPartial() {
        com.ztkj.im.protobuf.MessageProtobuf.Head result = new com.ztkj.im.protobuf.MessageProtobuf.Head(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.ztkj.im.protobuf.MessageProtobuf.Head result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgId_ = msgId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.msgType_ = msgType_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.msgContentType_ = msgContentType_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.fromId_ = fromId_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.toId_ = toId_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.timestamp_ = timestamp_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.statusReport_ = statusReport_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.extend_ = extend_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.ztkj.im.protobuf.MessageProtobuf.Head) {
          return mergeFrom((com.ztkj.im.protobuf.MessageProtobuf.Head)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.ztkj.im.protobuf.MessageProtobuf.Head other) {
        if (other == com.ztkj.im.protobuf.MessageProtobuf.Head.getDefaultInstance()) return this;
        if (!other.getMsgId().isEmpty()) {
          msgId_ = other.msgId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getMsgType() != 0) {
          setMsgType(other.getMsgType());
        }
        if (other.getMsgContentType() != 0) {
          setMsgContentType(other.getMsgContentType());
        }
        if (!other.getFromId().isEmpty()) {
          fromId_ = other.fromId_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getToId().isEmpty()) {
          toId_ = other.toId_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.getTimestamp() != 0L) {
          setTimestamp(other.getTimestamp());
        }
        if (other.getStatusReport() != 0) {
          setStatusReport(other.getStatusReport());
        }
        if (!other.getExtend().isEmpty()) {
          extend_ = other.extend_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                msgId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                msgType_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                msgContentType_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                fromId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                toId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                timestamp_ = input.readInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                statusReport_ = input.readInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                extend_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object msgId_ = "";
      /**
       * <pre>
       * 消息id
       * </pre>
       *
       * <code>string msgId = 1;</code>
       * @return The msgId.
       */
      public java.lang.String getMsgId() {
        java.lang.Object ref = msgId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          msgId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 消息id
       * </pre>
       *
       * <code>string msgId = 1;</code>
       * @return The bytes for msgId.
       */
      public com.google.protobuf.ByteString
          getMsgIdBytes() {
        java.lang.Object ref = msgId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msgId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 消息id
       * </pre>
       *
       * <code>string msgId = 1;</code>
       * @param value The msgId to set.
       * @return This builder for chaining.
       */
      public Builder setMsgId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        msgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息id
       * </pre>
       *
       * <code>string msgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgId() {
        msgId_ = getDefaultInstance().getMsgId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息id
       * </pre>
       *
       * <code>string msgId = 1;</code>
       * @param value The bytes for msgId to set.
       * @return This builder for chaining.
       */
      public Builder setMsgIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        msgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int msgType_ ;
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>int32 msgType = 2;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>int32 msgType = 2;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {

        msgType_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>int32 msgType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private int msgContentType_ ;
      /**
       * <pre>
       * 消息内容类型
       * </pre>
       *
       * <code>int32 msgContentType = 3;</code>
       * @return The msgContentType.
       */
      @java.lang.Override
      public int getMsgContentType() {
        return msgContentType_;
      }
      /**
       * <pre>
       * 消息内容类型
       * </pre>
       *
       * <code>int32 msgContentType = 3;</code>
       * @param value The msgContentType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgContentType(int value) {

        msgContentType_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息内容类型
       * </pre>
       *
       * <code>int32 msgContentType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgContentType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        msgContentType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fromId_ = "";
      /**
       * <pre>
       * 消息发送者id
       * </pre>
       *
       * <code>string fromId = 4;</code>
       * @return The fromId.
       */
      public java.lang.String getFromId() {
        java.lang.Object ref = fromId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          fromId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 消息发送者id
       * </pre>
       *
       * <code>string fromId = 4;</code>
       * @return The bytes for fromId.
       */
      public com.google.protobuf.ByteString
          getFromIdBytes() {
        java.lang.Object ref = fromId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fromId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 消息发送者id
       * </pre>
       *
       * <code>string fromId = 4;</code>
       * @param value The fromId to set.
       * @return This builder for chaining.
       */
      public Builder setFromId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fromId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息发送者id
       * </pre>
       *
       * <code>string fromId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromId() {
        fromId_ = getDefaultInstance().getFromId();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息发送者id
       * </pre>
       *
       * <code>string fromId = 4;</code>
       * @param value The bytes for fromId to set.
       * @return This builder for chaining.
       */
      public Builder setFromIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        fromId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object toId_ = "";
      /**
       * <pre>
       * 消息接收者id
       * </pre>
       *
       * <code>string toId = 5;</code>
       * @return The toId.
       */
      public java.lang.String getToId() {
        java.lang.Object ref = toId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          toId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 消息接收者id
       * </pre>
       *
       * <code>string toId = 5;</code>
       * @return The bytes for toId.
       */
      public com.google.protobuf.ByteString
          getToIdBytes() {
        java.lang.Object ref = toId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          toId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 消息接收者id
       * </pre>
       *
       * <code>string toId = 5;</code>
       * @param value The toId to set.
       * @return This builder for chaining.
       */
      public Builder setToId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        toId_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息接收者id
       * </pre>
       *
       * <code>string toId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearToId() {
        toId_ = getDefaultInstance().getToId();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息接收者id
       * </pre>
       *
       * <code>string toId = 5;</code>
       * @param value The bytes for toId to set.
       * @return This builder for chaining.
       */
      public Builder setToIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        toId_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private long timestamp_ ;
      /**
       * <pre>
       * 消息时间戳
       * </pre>
       *
       * <code>int64 timestamp = 6;</code>
       * @return The timestamp.
       */
      @java.lang.Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <pre>
       * 消息时间戳
       * </pre>
       *
       * <code>int64 timestamp = 6;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {

        timestamp_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息时间戳
       * </pre>
       *
       * <code>int64 timestamp = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private int statusReport_ ;
      /**
       * <pre>
       * 状态报告
       * </pre>
       *
       * <code>int32 statusReport = 7;</code>
       * @return The statusReport.
       */
      @java.lang.Override
      public int getStatusReport() {
        return statusReport_;
      }
      /**
       * <pre>
       * 状态报告
       * </pre>
       *
       * <code>int32 statusReport = 7;</code>
       * @param value The statusReport to set.
       * @return This builder for chaining.
       */
      public Builder setStatusReport(int value) {

        statusReport_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态报告
       * </pre>
       *
       * <code>int32 statusReport = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatusReport() {
        bitField0_ = (bitField0_ & ~0x00000040);
        statusReport_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object extend_ = "";
      /**
       * <pre>
       * 扩展字段，以key/value形式存放的json
       * </pre>
       *
       * <code>string extend = 8;</code>
       * @return The extend.
       */
      public java.lang.String getExtend() {
        java.lang.Object ref = extend_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          extend_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 扩展字段，以key/value形式存放的json
       * </pre>
       *
       * <code>string extend = 8;</code>
       * @return The bytes for extend.
       */
      public com.google.protobuf.ByteString
          getExtendBytes() {
        java.lang.Object ref = extend_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extend_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 扩展字段，以key/value形式存放的json
       * </pre>
       *
       * <code>string extend = 8;</code>
       * @param value The extend to set.
       * @return This builder for chaining.
       */
      public Builder setExtend(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        extend_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 扩展字段，以key/value形式存放的json
       * </pre>
       *
       * <code>string extend = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtend() {
        extend_ = getDefaultInstance().getExtend();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 扩展字段，以key/value形式存放的json
       * </pre>
       *
       * <code>string extend = 8;</code>
       * @param value The bytes for extend to set.
       * @return This builder for chaining.
       */
      public Builder setExtendBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        extend_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Head)
    }

    // @@protoc_insertion_point(class_scope:Head)
    private static final com.ztkj.im.protobuf.MessageProtobuf.Head DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.ztkj.im.protobuf.MessageProtobuf.Head();
    }

    public static com.ztkj.im.protobuf.MessageProtobuf.Head getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Head>
        PARSER = new com.google.protobuf.AbstractParser<Head>() {
      @java.lang.Override
      public Head parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Head> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Head> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.ztkj.im.protobuf.MessageProtobuf.Head getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Head_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Head_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tmsg.proto\"(\n\003Msg\022\023\n\004head\030\001 \001(\0132\005.Head\022" +
      "\014\n\004body\030\002 \001(\t\"\225\001\n\004Head\022\r\n\005msgId\030\001 \001(\t\022\017\n" +
      "\007msgType\030\002 \001(\005\022\026\n\016msgContentType\030\003 \001(\005\022\016" +
      "\n\006fromId\030\004 \001(\t\022\014\n\004toId\030\005 \001(\t\022\021\n\ttimestam" +
      "p\030\006 \001(\003\022\024\n\014statusReport\030\007 \001(\005\022\016\n\006extend\030" +
      "\010 \001(\tB\'\n\024com.ztkj.im.protobufB\017MessagePr" +
      "otobufb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Msg_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Msg_descriptor,
        new java.lang.String[] { "Head", "Body", });
    internal_static_Head_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Head_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Head_descriptor,
        new java.lang.String[] { "MsgId", "MsgType", "MsgContentType", "FromId", "ToId", "Timestamp", "StatusReport", "Extend", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
