package com.ztkj.baselib.base

import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

/**
 * @CreateTime : 2022/10/12 10:25
 * <AUTHOR> AppOS
 * @Description :
 */
class ApplicationObserver: LifecycleEventObserver {
    companion object{
        private const val TAG = "ApplicationObserver"
    }
    /**
     * Called when a state transition event happens.
     *
     * @param source The source of the event
     * @param event The event
     */
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when(event){
            Lifecycle.Event.ON_CREATE -> Log.i(TAG, "onStateChanged: ON_CREATE", )
            Lifecycle.Event.ON_START -> Log.i(TAG, "onStateChanged: ON_START", )
            Lifecycle.Event.ON_RESUME -> Log.i(TAG, "onStateChanged: ON_RESUME", )
            Lifecycle.Event.ON_PAUSE -> Log.i(TAG, "onStateChanged: ON_PAUSE", )
            Lifecycle.Event.ON_STOP -> Log.i(TAG, "onStateChanged: ON_STOP", )
            Lifecycle.Event.ON_DESTROY -> Log.i(TAG, "onStateChanged: ON_DESTROY", )
            Lifecycle.Event.ON_ANY -> Log.i(TAG, "onStateChanged: ")
        }

    }
}