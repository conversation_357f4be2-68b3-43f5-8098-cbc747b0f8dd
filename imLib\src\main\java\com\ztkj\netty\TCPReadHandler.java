package com.ztkj.netty;

import com.ztkj.im.protobuf.MessageProtobuf;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       TCPReadHandler.java</p>
 * <p>@PackageName:     com.freddy.im.netty</p>
 * <b>
 * <p>@Description:     消息接收处理handler</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/07 21:40</p>
 * <p>@email:           <EMAIL></p>
 */
public class TCPReadHandler extends ChannelInboundHandlerAdapter {

    private TcpClient tcpClient;

    public TCPReadHandler(TcpClient tcpClient) {
        this.tcpClient = tcpClient;
    }


    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        super.channelInactive(ctx);
        System.err.println("TCPReadHandler channelInactive()");
        Channel channel = ctx.channel();
        if (channel != null) {
            channel.close();
            ctx.close();
        }
        // 触发重连
        tcpClient.resetConnect(false);
    }



    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        super.exceptionCaught(ctx, cause);
        System.err.println("TCPReadHandler exceptionCaught()");
        Channel channel = ctx.channel();
        if (channel != null) {
            channel.close();
            ctx.close();
        }
        // 触发重连
        tcpClient.resetConnect(false);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ByteBuf buf = (ByteBuf) msg;
        if (buf.readableBytes() <= 0) {
            return;
        }
        byte[] bs = new byte[buf.readableBytes()];
        buf.readBytes(bs);
        String rec = ByteUtil.bytes2HexStr(bs);
//        System.out.println(rec);
        tcpClient.receivedMsg(getReceiveMsg(rec));
    }

    private MessageProtobuf.Msg getReceiveMsg(String content) {
        MessageProtobuf.Msg.Builder builder = MessageProtobuf.Msg.newBuilder();
        MessageProtobuf.Head.Builder headBuilder = MessageProtobuf.Head.newBuilder();
        builder.setHead(headBuilder);
        builder.setBody(content);
        return builder.build();
    }

}
