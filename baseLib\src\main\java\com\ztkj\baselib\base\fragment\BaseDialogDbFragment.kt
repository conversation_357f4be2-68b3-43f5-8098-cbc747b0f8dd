package com.ztkj.baselib.base.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.inflateBindingWithGeneric

/**
 * @CreateTime : 2022/10/25 15:08
 * <AUTHOR> AppOS
 * @Description :
 */
abstract class BaseDialogDbFragment<VM : BaseViewModel, DB : ViewDataBinding>: BaseVmDialogFragment<VM>() {

    //该类绑定的ViewDataBinding
    private var _binding: DB? = null
    val binding: DB get() = _binding!!


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding  = inflateBindingWithGeneric(inflater,container,false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }


}