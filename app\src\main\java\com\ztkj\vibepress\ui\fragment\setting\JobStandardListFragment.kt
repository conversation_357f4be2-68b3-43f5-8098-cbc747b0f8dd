package com.ztkj.vibepress.ui.fragment.setting

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.lifecycle.lifecycleOwner
import com.chad.library.adapter.base.QuickAdapterHelper
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.navigateAction
import com.ztkj.baselib.ext.parseState
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.databinding.FragmentJobStandardListBinding
import com.ztkj.vibepress.ui.adapter.JobListHeaderAdapter
import com.ztkj.vibepress.ui.adapter.JobStandardListAdapter
import com.ztkj.vibepress.viewmodel.request.RequestJobStandardViewModel
import com.ztkj.vibepress.viewmodel.state.JobStandardViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 强夯设备属性管理
 */
@AndroidEntryPoint
class JobStandardListFragment : BaseFragment<BaseViewModel, FragmentJobStandardListBinding>() {

    private val jobStandardViewModel: JobStandardViewModel by viewModels()

    private val requestJobStandardViewModel: RequestJobStandardViewModel by viewModels()


    private val jobStandardListAdapter by lazy(LazyThreadSafetyMode.NONE) {
        JobStandardListAdapter()
    }

    private val helper by lazy(LazyThreadSafetyMode.NONE) {
        QuickAdapterHelper.Builder(jobStandardListAdapter)
            .build()
            .addBeforeAdapter(JobListHeaderAdapter())
    }

    override fun initView(savedInstanceState: Bundle?) {
        initToolbar()
        initRecyclerView()
    }


    private fun initToolbar() {
        binding.toolBar.run {
            initNormalClose(this@JobStandardListFragment)
            rightButtonClick {
                createNewJobStandard()
            }
        }
    }

    /**
     * 创建新的作业标准
     */
    private fun createNewJobStandard() {
        nav().navigateAction(
            R.id.action_jobStandardListFragment_to_jobStandardFragment
        )
//        showWorkTypeSelectDialog()
    }


    private fun initRecyclerView() {
        binding.rvData.adapter = helper.adapter
        binding.rvData.addItemDecoration(JobStandardListAdapter.DividerItemDecoration(requireContext()))
        addButtonListener()
    }

    private fun addButtonListener() {
        jobStandardListAdapter.addOnItemChildClickListener(R.id.setToCurrent) { adapter, _, position ->
            val jobStandardEntity = adapter.items[position]
            CacheUtil.setJobStandard(jobStandardEntity)
            "设置作业标准成功".toast()
        }.addOnItemChildClickListener(R.id.modify) { adapter, view, position ->
            nav().navigateAction(
                R.id.action_jobStandardListFragment_to_jobStandardFragment,
                Bundle().apply {
                    putLong(
                        Constants.NAVIGATION_PARAMS.JOB_STANDARD_LIST_TO_DETAIL,
                        adapter.items[position].uID
                    )
                })
        }
        jobStandardListAdapter.addOnItemChildClickListener(R.id.delete) { adapter, _, position ->
            val currentJobStandard = appViewModel.jobStandardEntity.value
            val uId = adapter.items[position].uID
            if (uId == currentJobStandard?.uID) {
                "不允许删除当前的作业标准!".toast()
            } else {
                showConfirmDeleteDialog(adapter.items[position])
            }

        }
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
//        requestJobStandardViewModel.getJobStandardList()
        lifecycleScope.launch {
            jobStandardViewModel.getJobStandardList().collect {
                jobStandardListAdapter.submitList(it)
            }
        }

    }

    override fun createObserver() {
        super.createObserver()
        requestJobStandardViewModel.jobStandardListResult.observe(this@JobStandardListFragment) { resultState ->
            parseState(resultState, {
                jobStandardViewModel.deleteAll()
                jobStandardViewModel.insertAll(entities = it)
            }, {

            })
        }
    }

    private fun showConfirmDeleteDialog(entity: JobStandardEntity) {
        MaterialDialog(requireContext()).show {
            title(R.string.tip)
            icon(R.mipmap.ic_tips)
            message(R.string.confirm_delete_tips)
            positiveButton(R.string.confirm) {
                deleteLocalDataByUid(entity.uID)
                requestJobStandardViewModel.deleteJobStandardById(entity.id)
            }
            negativeButton(R.string.cancel)
            lifecycleOwner(this@JobStandardListFragment)

        }
    }

    /**
     * 根据Uid 删除本地数据
     */
    private fun deleteLocalDataByUid(uId: Long) {
        jobStandardViewModel.deleteEntityByUid(uId)
    }


}