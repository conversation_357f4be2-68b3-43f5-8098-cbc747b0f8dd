package com.ztkj.vibepress.data.model.bean

import android.annotation.SuppressLint
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * 夯机设备 实体类
 * @CreateTime : 2023/5/30 14:37
 * <AUTHOR> AppOS
 * @Description : 夯机设备 实体类
 */
@Entity
@SuppressLint("ParcelCreator")
@Parcelize
data class TampDeviceEntity(
    @PrimaryKey
    val uID: Long = 0,
    /**
     * 机构ID
     */
    @ColumnInfo(name = "deptId") var deptId: Long? = null,
    /**
     *名称
     */
    @ColumnInfo(name = "name") val name: String,
    /**
     * 序列号(设备编号)
     */
    @ColumnInfo(name = "serialNumber") var serialNum: String,
    /**
     * 水平距离
     */
    @ColumnInfo @SerializedName("ftxDiatance") val horizontalDistance: Double,
    /**
     * 垂直距离
     */
    @ColumnInfo @SerializedName("zftxVerticalDiatance") val verticalDistance: Double,
    /*    */
    /**
     * 双天线与重锤角度
     *//*
    @ColumnInfo val aerialsHammerAngle: Double,*/
    /**
     * 安装方式
     */
    @ColumnInfo val installsWay: String,

    /*    */
    /**
     * 安装位置
     *//*
    @ColumnInfo val installsLoc: String,*/

    /**
     * 天线安装高度
     */
    @ColumnInfo val antennaHeight: Double,
    /**
     * 拉锁数量(默认：四拉锁)
     */
    @ColumnInfo val cableCount: Int = 4,

    /**
     * 基准点
     */
    @ColumnInfo val referencePoint: Double,
    /**
     * 磁铁个数
     */
    @ColumnInfo val magnetNum: Int,
    /**
     * 卷扬机周长
     */
    @ColumnInfo val windingEnginePerimeter: Double,
    /**
     * 磁铁间距
     */
    @ColumnInfo val magnetDistance: Double,
    /**
     * 夯击过滤阈值
     */
    @ColumnInfo val hitThreshold: Double,
    /**
     * 聚合系数
     */
    @ColumnInfo val clusterThreshold: Double,

    /**
     * 张力阈值
     */
    @ColumnInfo val tensionThreshold: Double,
    @ColumnInfo("id") val id: String?
) : Parcelable, Serializable {
    fun checkValidData(): Boolean {
        // 校验requireTimes
        verticalDistance.let {
            if (it < -50 || it > 50) {
                return false
            }
        }
        horizontalDistance.let {
            if (it < -50 || it > 50) {
                return false
            }
        }
        antennaHeight.let {
            if (it < 0 || it > 100) {
                return false
            }
        }
        cableCount.let {
            if (it < 0 || it > 10) {
                return false
            }
        }
        magnetNum.let {
            if (it < 0 || it > 10000) {
                return false
            }
        }
        windingEnginePerimeter.let {
            if (it < 0 || it > 20) {
                return false
            }
        }
        magnetDistance.let {
            if (it < 0 || it > 100) {
                return false
            }
        }
        hitThreshold.let {
            if (it < 0 || it > 1) {
                return false
            }
        }
        clusterThreshold.let {
            if (it < 0 || it > 5) {
                return false
            }
        }
        tensionThreshold.let {
            if (it < 0 || it > 10000) {
                return false
            }
        }

        return true
    }
}
