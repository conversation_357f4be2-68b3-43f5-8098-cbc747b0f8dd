plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id("kotlin-parcelize")

    id 'kotlin-kapt'
    id("com.google.dagger.hilt.android")
}

android {
    namespace 'com.ztkj.vibepress'
    compileSdk 33

    defaultConfig {
        applicationId "com.ztkj.vibepress"
        minSdk 23
        targetSdk 33
        versionCode 110
        versionName "1.0.110"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "arm64-v8a", "armeabi-v7a", "armeabi"//, "x86", "mips"
        }
    }

    buildTypes {
        //自定义打包输出文件名
        applicationVariants.all { variant ->
            variant.outputs.all { output ->
                def outputFile = output.outputFile
                def fileName
                if (outputFile != null && outputFile.name.endsWith('.apk')) {
                    if (variant.buildType.name == 'release') {//如果是release包
                        fileName = "${defaultConfig.applicationId}_${defaultConfig.versionName}" + "_" + new Date().format("yyyyMMdd") + ".apk"
                    } else if (variant.buildType.name == 'debug') {//如果是debug包
                        fileName = "${defaultConfig.applicationId}_${defaultConfig.versionName}" + "_" + new Date().format("yyyyMMdd") + ".apk"
                    }
                    outputFileName = fileName
                }
            }
        }

        debug {
            minifyEnabled false//开启混淆
            shrinkResources false
            zipAlignEnabled true//去除无用资源
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false//开启混淆
            shrinkResources false
            zipAlignEnabled true//去除无用资源
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/NOTICE', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/NOTICE.txt', 'META-INF/INDEX.LIST', 'META-INF/io.netty.versions.properties']
        }
    }

    signingConfigs {
        release {
            keyAlias 'ztkj'
            keyPassword 'ztkj2022'
            storeFile file('../ztkj.keystore')
            storePassword 'ztkj2022'
        }
    }


    buildFeatures {
        //这2个为非必选，想用哪个就保留那个 用的话一定要加上项目中的 ViewBinding & DataBinding 混淆规则
        dataBinding = true
        viewBinding = true
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation project(path: ':baseLib')
    implementation project(path: ':mapCore')
    implementation project(path: ':mapExt')
    implementation project(path: ':imLibExt')
    implementation project(path: ':imLib')
    implementation project(path: ':sensorLib')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    //dialog
    implementation "com.afollestad.material-dialogs:lifecycle:3.3.0"
    implementation "com.afollestad.material-dialogs:core:3.3.0"
    implementation 'com.afollestad.material-dialogs:input:3.3.0'
    implementation "com.afollestad.material-dialogs:bottomsheets:3.3.0"
    //微信开源项目，替代SP
    implementation 'com.tencent:mmkv:1.2.13'

    //管理界面状态库
    implementation 'com.kingja.loadsir:loadsir:1.3.8'
    //轮播图
    implementation 'com.github.zhpanvip:BannerViewPager:3.5.7'
    //屏幕适配
//    implementation 'me.jessyan:autosize:1.2.1'
    //multidex
    implementation 'androidx.multidex:multidex:2.0.1'
    //沉浸式布局
    implementation 'com.gitee.zackratos:UltimateBarX:0.8.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"

    //BaseAdapter
    implementation "io.github.cymchad:BaseRecyclerViewAdapterHelper:4.0.0-beta14"
    //google recyclerView
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    //第三方recyclerview
    implementation 'com.yanzhenjie.recyclerview:x:1.3.2'
    //leakcanary 内存溢出
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.9.1'
    //防崩溃
    implementation 'cat.ereza:customactivityoncrash:2.4.0'
    //bugly
    implementation 'com.tencent.bugly:crashreport:4.1.9'
    //Airbnb Lottie
    implementation 'com.airbnb.android:lottie:5.2.0'
    //basePopup
    implementation 'io.github.razerdp:BasePopup:3.2.0'
    //App更新
    implementation 'com.github.xuexiangjys:XUpdate:2.1.4'
    implementation 'com.github.xuexiangjys.XUpdateAPI:xupdate-easy:1.0.1'

    implementation("com.google.dagger:hilt-android:2.44")
    kapt("com.google.dagger:hilt-android-compiler:2.44")

    // To use Kotlin annotation processing tool (kapt)
    kapt("androidx.room:room-compiler:2.5.0")
    // optional - Kotlin Extensions and Coroutines support for Room
    implementation("androidx.room:room-ktx:2.5.0")

    implementation 'org.jetbrains.kotlin:kotlin-reflect:1.6.0'
    //spinner
    implementation('com.github.skydoves:powerspinner:1.2.7')

    implementation 'com.elvishew:xlog:1.11.0'

}

// Allow references to generated code
kapt {
    correctErrorTypes = true
}