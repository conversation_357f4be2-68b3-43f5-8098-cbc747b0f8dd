<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="160dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/item_setting_border"
    android:layout_height="128dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/inner_bg"
        android:layout_width="114dp"
        android:layout_height="76.8dp"
        android:layout_marginTop="11.2dp"
        android:contentDescription="@string/account"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/setting_item_inner_bg" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/inner_desc_bg"
        android:layout_width="43.2dp"
        android:layout_height="40.8dp"
        android:layout_marginTop="26.4dp"
        app:srcCompat="@mipmap/item_setting_diagnosis"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:text="@string/device_attribute"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/inner_bg"
        android:textColor="@color/white" />


</androidx.constraintlayout.widget.ConstraintLayout>