package com.ztkj.imext.netty;

import android.util.Log;

import com.blankj.utilcode.util.NetworkUtils;
import com.ztkj.im.listener.OnEventListener;
import com.ztkj.im.protobuf.MessageProtobuf;
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMSEventListener.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im</p>
 * <b>
 * <p>@Description:     与ims交互的listener</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/07 23:55</p>
 * <p>@email:           <EMAIL></p>
 */
public class TcpEventListener implements OnEventListener {

    public TcpEventListener() {
    }

    /**
     * 从差分软件接收到的数据
     *
     * @param msg
     */
    @Override
    public void dispatchMsg(MessageProtobuf.Msg msg) {
        final String xysj = msg.getBody();
//        Log.d("TAG", "从差分服务器收到的数据: " + xysj);
        NmeaAnalysisUtil.getInstance().setCfResponseData(xysj);
        NmeaAnalysisUtil.getInstance().cfResponseLiveData.postValue(xysj);
//        System.out.println("从差分服务器收到的数据: " + xysj);
        //TODO  需要修改
/*        EventBus.getDefault().post(new CFEvent(xysj));
//        EventBus.getDefault().post(new DataMessageDTO(xysj, null));*/
    }

    /**
     * 网络是否可用
     *
     * @return
     */
    @Override
    public boolean isNetworkAvailable() {
        return NetworkUtils.isAvailable();
    }

    @Override
    public int getReconnectInterval() {
        return 0;
    }

    @Override
    public int getConnectTimeout() {
        return 0;
    }

    @Override
    public int getForegroundHeartbeatInterval() {
        return 0;
    }

    @Override
    public int getBackgroundHeartbeatInterval() {
        return 0;
    }

    @Override
    public MessageProtobuf.Msg getHandshakeMsg() {
        return null;
    }

    @Override
    public MessageProtobuf.Msg getHeartbeatMsg() {
        return null;
    }

    @Override
    public int getServerSentReportMsgType() {
        return 0;
    }

    @Override
    public int getClientReceivedReportMsgType() {
        return 0;
    }

    @Override
    public int getResendCount() {
        return 0;
    }

    @Override
    public int getResendInterval() {
        return 0;
    }
}
