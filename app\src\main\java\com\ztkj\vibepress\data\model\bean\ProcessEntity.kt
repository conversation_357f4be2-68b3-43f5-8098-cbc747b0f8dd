package com.ztkj.vibepress.data.model.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.io.Serializable

@Parcelize
data class ProcessEntity(
    val carLatitude: String,
    val carLongitude: String,
    val countNumber: Int,
    val deviceId: String,
    val dropDis: String,
    val hammerHeight: String,
    val hitEnergy: String,
    val hitNumber: Int,
    val hjNumber: String,
    val stakeCode: String,
    val status: Int,
    val updateTime: String,
    val workTypeName: String
) : Parcelable, Serializable
