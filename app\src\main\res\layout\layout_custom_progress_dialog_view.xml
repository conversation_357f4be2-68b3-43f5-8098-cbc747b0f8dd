<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp">
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:indeterminateTint="@color/colorAccent"
            android:indeterminateTintMode="src_atop" />

        <TextView
            android:id="@+id/loading_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="请求网络中..."
            android:textColor="#666"
            android:textSize="16dp" />
    </LinearLayout>
</androidx.cardview.widget.CardView>