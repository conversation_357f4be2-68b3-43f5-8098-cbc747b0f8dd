package com.ztkj.imext.netty;

import com.ztkj.netty.TcpClientFactory;
import com.ztkj.netty.TcpClientInterface;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMSClientBootstrap.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im</p>
 * <b>
 * <p>@Description:     应用层的imsClient启动器</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/08 00:25</p>
 * <p>@email:           <EMAIL></p>
 */
public class TcpClientBootstrap {

    private static final TcpClientBootstrap INSTANCE = new TcpClientBootstrap();
    private TcpClientInterface tcpClient;
    private boolean isActive;

    private TcpClientBootstrap() {

    }

    public static TcpClientBootstrap getInstance() {
        return INSTANCE;
    }

/*    public static void main(String[] args) {
        String userId = "100001";
        String token = "token_" + userId;
        TcpClientBootstrap bootstrap = TcpClientBootstrap.getInstance();
        String hosts = "[{\"host\":\"127.0.0.1\", \"port\":8866}]";
        bootstrap.init(userId, token, hosts, 0);
    }*/

    public synchronized void init(String host) {
        if (!isActive()) {
            isActive = true;
            System.out.println("init TcpClientBootstrap, servers=" + host);
            if (null != tcpClient) {
                tcpClient.close();
            }
            tcpClient = TcpClientFactory.getTcpClient();
            tcpClient.init(host, new TcpEventListener());
        }
    }

    public void stop(){
        isActive = false;
        if (null != tcpClient) {
            tcpClient.close();
        }
    }

    public boolean isActive() {
        return isActive;
    }

    /**
     * 发送消息
     *
     * @param msg
     */
    public void sendMessage(String msg) {
        if (isActive) {
            tcpClient.sendMsg(msg);
        }
    }

}
