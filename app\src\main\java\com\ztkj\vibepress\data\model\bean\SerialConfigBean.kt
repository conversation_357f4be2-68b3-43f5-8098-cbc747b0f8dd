package com.ztkj.vibepress.data.model.bean

import android.os.Parcelable
import com.ztkj.vibepress.app.ext.checkAllPropertiesNotNull
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * @CreateTime : 2023/6/14 10:04
 * <AUTHOR> AppOS
 * @Description :
 */
@Parcelize
data class SerialConfigBean(
    /**
     * RTK串口Path，内置GNSS串口Path
     */
    var rtkPath: String,
    /**
     * RTK串口波特率，内置GNSS串口波特率
     */
    var rtkBaudrate: String,
    /**
     * 外置GNSS串口Path
     */
    var externalGnssPath: String,
    /**
     * 外置GNSS串口波特率
     */
    var externalGnssBaudrate: String,
    /**
     * 计数器串口路径
     */
    var counterPath: String,
    /**
     * 计数器串口波特率
     */
    var counterBaudrate: String
) : Parcelable, Serializable {

    fun isValid() = checkAllPropertiesNotNull(this)
}

