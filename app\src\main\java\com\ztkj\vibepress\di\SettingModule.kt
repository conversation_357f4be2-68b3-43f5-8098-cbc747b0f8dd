package com.ztkj.vibepress.di

import com.ztkj.vibepress.ui.adapter.SettingItemSpacingDecoration
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.hilt.android.scopes.ActivityScoped
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

/**
 * @CreateTime : 2023/6/8 11:01
 * <AUTHOR> AppOS
 * @Description :
 */
@Module
@InstallIn(ActivityComponent::class)
object SettingModule {

    @Named("SettingItem")
    @Provides
    fun provideSettingItemSpacingDecoration(): SettingItemSpacingDecoration {
        return SettingItemSpacingDecoration(40, 25)
    }


    @Named("DiagnosisItem")
    @Provides
    fun provideDiagnosisItemSpacingDecoration(): SettingItemSpacingDecoration {
        return SettingItemSpacingDecoration(20, 0)
    }
}