<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.appcompat.widget.AppCompatEditText
        android:layout_marginTop="12dp"
        android:id="@+id/etHammerWeight"
        android:hint="@string/hammerWeight"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:textColor="#333333"
        android:inputType="numberDecimal"
        android:background="@drawable/item_setting_border3"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="16dp"
        android:maxLines="1"
        android:layout_width="220dp"
        android:layout_height="48dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:text="锤重："
        android:textColor="#666666"
        android:textSize="18sp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/etHammerWeight"
        app:layout_constraintTop_toTopOf="@id/etHammerWeight"
        app:layout_constraintBottom_toBottomOf="@id/etHammerWeight"
        android:layout_marginEnd="8dp"
        android:gravity="end" />

    <androidx.appcompat.widget.AppCompatEditText
        android:layout_marginTop="12dp"
        android:id="@+id/etHammerRadius"
        android:hint="@string/hammerRadius"
        android:paddingStart="5dp"
        android:textColor="#333333"
        android:paddingEnd="5dp"
        android:inputType="numberDecimal"
        android:background="@drawable/item_setting_border3"
        app:layout_constraintTop_toBottomOf="@id/etHammerWeight"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="16dp"
        android:maxLines="1"
        android:layout_width="220dp"
        android:layout_height="48dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/textView"
        android:text="锤半径："
        android:textSize="18sp"
        android:textColor="#666666"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/etHammerRadius"
        app:layout_constraintTop_toTopOf="@id/etHammerRadius"
        app:layout_constraintBottom_toBottomOf="@id/etHammerRadius"
        android:layout_marginEnd="8dp"
        android:gravity="end" />


</androidx.constraintlayout.widget.ConstraintLayout>