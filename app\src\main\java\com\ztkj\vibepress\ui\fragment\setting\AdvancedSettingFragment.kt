package com.ztkj.vibepress.ui.fragment.setting

import android.os.Bundle
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.navigateAction
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.data.model.bean.SettingItem
import com.ztkj.vibepress.databinding.FragmentAdvancedSettingBinding
import com.ztkj.vibepress.ui.adapter.SettingAdapter
import com.ztkj.vibepress.ui.adapter.SettingItemSpacingDecoration
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import javax.inject.Named

@AndroidEntryPoint
class AdvancedSettingFragment : BaseFragment<BaseViewModel, FragmentAdvancedSettingBinding>() {

    @Inject
    lateinit var settingAdapter: SettingAdapter

    @Named("SettingItem")
    @Inject
    lateinit var settingItemSpacingDecoration: SettingItemSpacingDecoration

    override fun initView(savedInstanceState: Bundle?) {
        initToolbar()
        initRecyclerView()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        settingAdapter.submitList(settingItems)
    }

    private fun initToolbar() {
        binding.toolbar.initNormalClose(this)
    }

    private fun initRecyclerView() {
        binding.rvData.adapter = settingAdapter
        binding.rvData.addItemDecoration(settingItemSpacingDecoration)

        settingAdapter.setOnItemClickListener { _, _, position ->
            //Navigate to Different Pages Based on Different navigation actionID
            val data = settingAdapter.items[position]
            nav().navigateAction(data.actionId)
        }
    }

    private val settingItems: ArrayList<SettingItem>
        get() = arrayListOf(
            /*            SettingItem(
                            getString(R.string.network_parameters),
                            R.mipmap.network_param,
                            R.id.action_advancedSettingFragment_to_netParamFragment
                        ),*/
            SettingItem(
                getString(R.string.device_attribute),
                R.mipmap.device_attribute,
                R.id.action_advancedSettingFragment_to_deviceAttributeFragment
            ),
            SettingItem(
                getString(R.string.sensor_setting),
                R.mipmap.serial_parameters,
                R.id.action_advancedSettingFragment_to_multiSettingFragment
            ),
            /*            SettingItem(
                            getString(R.string.serial_parameters),
                            R.mipmap.serial_parameters,
                            R.id.action_advancedSettingFragment_to_serialParamFragment
                        ),
                        SettingItem(
                            getString(R.string.board_setting),
                            R.mipmap.board_setting,
                            R.id.action_advancedSettingFragment_to_boardSettingFragment
                        )*/
        )
}