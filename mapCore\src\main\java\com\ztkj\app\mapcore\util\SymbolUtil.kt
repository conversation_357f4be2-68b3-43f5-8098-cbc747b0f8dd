package com.ztkj.app.mapcore.util

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import com.esri.arcgisruntime.symbology.CompositeSymbol
import com.esri.arcgisruntime.symbology.PictureMarkerSymbol
import com.esri.arcgisruntime.symbology.SimpleLineSymbol
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol
import com.esri.arcgisruntime.symbology.Symbol
import com.esri.arcgisruntime.symbology.TextSymbol
import com.ztkj.baselib.base.appContext
import java.util.concurrent.ExecutionException

object SymbolUtil {

    private const val pointJson = "{\n" +
            "    \"type\": \"esriPMS\",\n" +
            "    \"url\": \"04f8451f113ea4991094cddc0cd3359e\",\n" +
            "    \"imageData\": \"iVBORw0KGgoAAAANSUhEUgAAABIAAAAcCAYAAABsxO8nAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABHElEQVQ4jbWUu23DMBRFzwM8hCdITY+RBdJrgajOBKrtBVQnC3iMsM4C0RYvBT9mKFKk7OQChCQCOrjve+CPdPhXkIIBnv0BOAKfwAJM4p7bIIUBePM/pwrQQWEUmKsghTMO1NJZwQiMK5APpwcSNCjMAjZ3dNkBCfoAniLIuzF3gI4+RBscne6ABJ1IQHmF9sjALUf2AdCSgr4fAF0jSMCqI+8N0ZbKP+Eaco9id0eQwOzHo7cNrJRAiav3TtCUfvwCCVzVxdxyZcUnueYI4AX4aoBe84sVSGBRF3ttgOOgthwhMGoZtJDlZhPkNbJuh+J23AQV2sHmW7HXEbg9bZL3qlqgiVuuqm6aoKSClCq1x1HTSTeo5aQb1KsfZyRDZErh76cAAAAASUVORK5CYII=\",\n" +
            "    \"contentType\": \"image/png\",\n" +
            "    \"width\": 13,\n" +
            "    \"height\": 21,\n" +
            "    \"angle\": 0,\n" +
            "    \"xoffset\": 0,\n" +
            "    \"yoffset\": 0\n" +
            "}"

    var pointSymbol: Symbol = buildSymbol(pointJson)

    fun buildSymbol(json: String): Symbol {
        return Symbol.fromJson(json)
    }

    /**
     * 构建图片标注
     */
    fun buildSimpleTampingDesignPoint(): SimpleMarkerSymbol {
        val simpleMarkerSymbol =
            SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.TRANSPARENT, 28.0f)
        simpleMarkerSymbol.outline =
            SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, Color.parseColor("#1bcad6"), 2.0f)
        return simpleMarkerSymbol
    }

    fun buildHighLightPoint(text: String): CompositeSymbol {
        // 创建 CompositeSymbol 将图片和文字符号组合在一起
        val compositeSymbol = CompositeSymbol()
        // 创建一个 SimpleMarkerSymbol
        val simpleMarkerSymbol =
            SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.parseColor("#5CFF23"), 32.0f)
        // 创建一个 TextSymbol 作为文字符号
        val textSymbol = TextSymbol(
            14f,
            text,
            Color.BLACK,
            TextSymbol.HorizontalAlignment.CENTER,
            TextSymbol.VerticalAlignment.MIDDLE
        )

        compositeSymbol.symbols.add(simpleMarkerSymbol)
        compositeSymbol.symbols.add(textSymbol)
        return compositeSymbol
    }

    fun buildTampingDesignPoint(text: String): CompositeSymbol {
        // 创建 CompositeSymbol 将图片和文字符号组合在一起
        val compositeSymbol = CompositeSymbol()
        // 创建一个 SimpleMarkerSymbol
        val simpleMarkerSymbol =
            SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.TRANSPARENT, 30.0f)
        simpleMarkerSymbol.outline =
            SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, Color.parseColor("#1bcad6"), 2.0f)
        // 创建一个 TextSymbol 作为文字符号
        val textSymbol = TextSymbol(
            14f,
            text,
            Color.WHITE,
            TextSymbol.HorizontalAlignment.CENTER,
            TextSymbol.VerticalAlignment.TOP
        )
        // 偏移文字的位置（向下偏移10个位置）
        textSymbol.offsetY = 40f
        compositeSymbol.symbols.add(simpleMarkerSymbol)
        compositeSymbol.symbols.add(textSymbol)
        return compositeSymbol

    }


    private fun buildPictureMarkerWithBitmapDrawable(
        bitmapDrawable: BitmapDrawable?,
        onComplete: (PictureMarkerSymbol?) -> Unit
    ) {
        val async = PictureMarkerSymbol.createAsync(bitmapDrawable)
        async.addDoneListener {
            try {
                val pictureMarkerSymbol = async.get()
                pictureMarkerSymbol.loadAsync()
                onComplete(pictureMarkerSymbol)
            } catch (e: Exception) {
                e.printStackTrace()
                onComplete(null)
            }
        }
    }

    fun buildTextSymbol(text: String, offset: Int): Symbol {
        val textSymbol = TextSymbol()
        textSymbol.text = text
        textSymbol.color = Color.DKGRAY
        textSymbol.size = 14f
        textSymbol.horizontalAlignment = TextSymbol.HorizontalAlignment.CENTER
        textSymbol.verticalAlignment = TextSymbol.VerticalAlignment.BOTTOM
//        textSymbol.haloColor = Color.WHITE
//        textSymbol.haloWidth = 4f
        textSymbol.offsetY = offset.toFloat()
        return textSymbol
    }

    fun buildCompositeSymbol(text: String): CompositeSymbol {
        val compositeSymbol = CompositeSymbol()

        val simpleMarkerSymbol =
            SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.rgb(69, 141, 48), 30.0f)
        compositeSymbol.symbols.add(simpleMarkerSymbol)

        val textSymbol = TextSymbol(
            12.0f,
            text,
            Color.WHITE,
            TextSymbol.HorizontalAlignment.CENTER,
            TextSymbol.VerticalAlignment.MIDDLE
        )
        compositeSymbol.symbols.add(textSymbol)

        return compositeSymbol

    }

}