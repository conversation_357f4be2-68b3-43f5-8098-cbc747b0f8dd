package com.ztkj.vibepress.app.widget.callback

import android.content.Context
import android.view.View
import com.kingja.loadsir.callback.Callback
import com.ztkj.vibepress.R

/**
 * @CreateTime : 2022/10/12 14:50
 * <AUTHOR> AppOS
 * @Description :
 */
class LoadingCallback : Callback() {

    override fun onCreateView(): Int {
        return R.layout.layout_loading
    }

    override fun onReloadEvent(context: Context?, view: View?): <PERSON><PERSON><PERSON> {
        return true
    }
}