package com.ztkj.app.mapcore

import androidx.lifecycle.LifecycleEventObserver
import com.esri.arcgisruntime.data.FeatureQueryResult
import com.esri.arcgisruntime.geometry.Geometry
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.mapping.view.MapView
import com.ztkj.app.mapcore.entity.GeoJsonModel

typealias MapQueryWmsResultListener =
        ((point: Point, layerName: String, bbox: String) -> Unit)?

/**
 * @Date: 2023/3/20 16:50
 * <AUTHOR> AppOS
 * @Description : 利用Lifecycle解耦MapView生命周期,具体方法在接口实现方法
 */
interface BaseMapHelper : LifecycleEventObserver {

    /**
     * 初始化地图
     * @param mapView MapView
     */
    fun init(mapView: MapView)

    /**
     * 获取 MapView,用于自定义扩展
     */
    val mMapView: MapView

    /**
     * 设置地图中心点
     * @param point 中心店位置
     * @param scale 缩放比例
     */
    fun setMapCenter(point: Point, scale: Double = mMapView.mapScale)

    fun getMapCenter(): Point

    fun mapZoomIn(interval: Double = 100.0)

    fun mapZoomOut(interval: Double = 100.0)

    fun loadFeatureLayer(url: String?)

    fun loadWmsLayer(url: String, names: List<String>)


    fun setMapOnTouchCallback(listener: MapQueryWmsResultListener)

    /**
     * 加载带刷新的WmsLayer
     */
    fun loadRefreshWmsLayer(url: String, names: List<String>, refreshIntervalMilliseconds: Long?)

    fun loadRefreshWmsLayer(
        url: String,
        names: List<String>,
        refreshIntervalMilliseconds: Long?,
        cqlFilter: String?
    )

    fun loadWfsLayer(url: String, tableName: String)

    fun loadWfsLayer(
        url: String,
        tableName: String,
        longitude: Double,
        latitude: Double,
        callback: (Map<String, Any>?) -> Unit
    )

    /**
     * 获取当前正在作业的夯点信息
     */
    fun loadNearestTampingPoint(
        url: String,
        tableName: String,
        callback: (Map<String, Any>?) -> Unit
    )

    /**
     * 获取当前正在作业的夯点列表信息
     */
    fun loadNearbyTampingPointList(
        url: String,
        tableName: String,
        callback: (FeatureQueryResult) -> Unit
    )

    fun drawHighlightPoint(text: String?, geometry: Geometry)


    fun drawHammer(point: Point, resId: Int, angle: Float = 0.0f)

    fun loadNearbyDesignPoint(geoJsonModel: GeoJsonModel)


}