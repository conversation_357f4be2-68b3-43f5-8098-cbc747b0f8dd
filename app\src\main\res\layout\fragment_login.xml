<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.fragment.login.LoginFragment">

    <data>
        
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="386dp"
            android:layout_height="340dp"
            android:orientation="vertical"
            android:background="@mipmap/item_bg_login"
            bind:layout_constraintBottom_toBottomOf="parent"
            bind:layout_constraintEnd_toEndOf="parent"
            bind:layout_constraintStart_toStartOf="parent"
            bind:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="16dp"
                android:background="@color/transparent"
                app:tabIndicatorColor="#2FF2FF"
                app:tabMinWidth="150dp"
                app:tabMaxWidth="200dp"
                app:tabIndicatorFullWidth="false"
                app:tabIndicatorGravity="bottom"
                app:tabMode="fixed"
                app:tabSelectedTextColor="#2FF2FF"
                app:tabTextAppearance="@style/MyTabLayout"
                app:tabTextColor="@color/white"
                bind:layout_constraintStart_toStartOf="parent"
                bind:layout_constraintTop_toTopOf="parent" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/view_pager2"
                android:layout_marginTop="10dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:layout_width="match_parent"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:layout_height="match_parent" />


        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>