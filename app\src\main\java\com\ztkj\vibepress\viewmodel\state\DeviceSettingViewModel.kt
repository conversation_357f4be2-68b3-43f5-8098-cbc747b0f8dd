package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.elvishew.xlog.XLog
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ztkj.vibepress.app.ext.showToast
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.data.reposity.TampDeviceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.NumberFormatException
import javax.inject.Inject


/**
 * @CreateTime : 2023/6/5 20:20
 * <AUTHOR> AppOS
 * @Description :
 */
@HiltViewModel
class DeviceSettingViewModel @Inject constructor(private val tampDeviceRepository: TampDeviceRepository) :
    ViewModel() {

    private val _updateStatus = MutableLiveData<Boolean>()

    val deviceAttributeError = MutableLiveData(false)

    val updateStatus: LiveData<Boolean>
        get() = _updateStatus

    fun insert(tampDeviceMap: Map<String, String>?) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val gson = Gson()
                val dataJson = gson.toJson(tampDeviceMap)
                val tampDeviceEntity = gson.fromJson(dataJson, TampDeviceEntity::class.java)
                tampDeviceEntity.serialNum = SerialNumberUtil.getSerial()
                if (tampDeviceEntity.checkValidData()) {
                    tampDeviceRepository.insert(tampDeviceEntity)
                    /**
                     * 上传数据到服务器
                     */
                    uploadTampDeviceEntity(tampDeviceEntity)
                } else {
                    deviceAttributeError.postValue(true)
                }

            } catch (e: NumberFormatException) {
                showToast("必要参数不能为空!!!")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    suspend fun getTampDeviceEntity(): TampDeviceEntity? {
        return tampDeviceRepository.getTampEntity()
    }

    suspend fun getTampDeviceDeviceMap(): MutableMap<String, String>? {
        val tampDeviceEntity = getTampDeviceEntity()
        return tampDeviceEntity?.let { entity ->
            val gson = Gson()
            val jsonString = gson.toJson(entity)
            val mapType = object : TypeToken<MutableMap<String, String>>() {}.type
            gson.fromJson(jsonString, mapType)
        }
    }

    suspend fun getLocalTampDeviceEntity(): TampDeviceEntity? =
        tampDeviceRepository.getTampEntity()

    fun insertTampDeviceEntity(entity: TampDeviceEntity) {
        viewModelScope.launch {
            tampDeviceRepository.insert(entity)
        }
    }

    private fun uploadTampDeviceEntity(entity: TampDeviceEntity) {
        viewModelScope.launch {
            val response = tampDeviceRepository.updateTampDeviceEntity(entity)
            _updateStatus.value = response.isSuccess()
        }
    }
}