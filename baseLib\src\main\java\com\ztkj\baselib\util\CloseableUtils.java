package com.ztkj.baselib.util;

import java.io.Closeable;
import java.io.IOException;

public class CloseableUtils {

    public static void close(final Closeable... closeables) {
        if (closeables != null && closeables.length > 0) {
            for (Closeable closeable : closeables) {
                if (closeable != null) {
                    try {
                        closeable.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

}
