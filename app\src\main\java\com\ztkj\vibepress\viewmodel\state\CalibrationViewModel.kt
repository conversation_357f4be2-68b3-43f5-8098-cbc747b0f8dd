package com.ztkj.vibepress.viewmodel.state

import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.callback.databinding.BooleanObservableField
import com.ztkj.baselib.callback.databinding.StringObservableField

/**
 * @CreateTime : 2023/7/26 16:13
 * <AUTHOR> AppOS
 * @Description :
 */
class CalibrationViewModel : BaseViewModel() {
    //是否点击了确定
    var isDoneClick = BooleanObservableField()

    /**
     * 传感器数据
     */
    var sensorDataValue = StringObservableField()

    /**
     * 张力传感器数据阈值
     */
    var tensionValue = StringObservableField()

    var liftingHeightRatio: Float = 0f
}