<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.VibePress" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:windowFullscreen">true</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="actionBarSize">@dimen/actionBarSize</item>
        <!-- Customize your theme here. -->
    </style>

    <!--    自定义高度的RecyclerView属性-->
    <declare-styleable name="MaxHeightRecyclerView">
        <!--        高度-->
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>

    <!--Overflow label text style-->
    <style name="MyOverflowText" parent="TextAppearance.AppCompat.Small">
        <item name="android:textColor">@color/orange</item>
    </style>

    <!--Floating label text style-->
    <style name="MyHintText" parent="TextAppearance.AppCompat.Small">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <!--Error label text style-->
    <style name="MyErrorText" parent="TextAppearance.AppCompat.Small">
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="FullscreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/colorAccent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <!--<item name="android:windowBackground">@mipmap/start_second</item>-->
    </style>

    <!--TabLayout字体大小-->
    <style name="MyTabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="tabPaddingStart">16dp</item>
        <item name="tabPaddingEnd">15dp</item>
    </style>

</resources>